#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "NavigationUtilities.generated.h"

enum class ENavigationTileType : uint8;
class UPaperSpriteComponent;
class UPrimitiveComponent;
class AXwPaperTileMapActor;
struct FGridTileMap;

/**
 * 导航工具类
 * 提供各种导航相关的实用函数
 */
UCLASS()
class DREAMBOY_API UNavigationUtilities : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:
	/**
	 * 计算组件占据的格子
	 * @param Component 组件
	 * @param TileMapActor 所在的TileMapActor
	 * @param OutOccupiedTiles 输出占据的格子
	 * @return 是否计算成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Utilities")
	static bool CalculateOccupiedTiles(UPrimitiveComponent* Component, AXwPaperTileMapActor* TileMapActor, TArray<FIntPoint>& OutOccupiedTiles);
	
	/**
	 * 计算Sprite组件占据的格子
	 * @param SpriteComponent Sprite组件
	 * @param TileMapActor 所在的TileMapActor
	 * @param OutOccupiedTiles 输出占据的格子
	 * @return 是否计算成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Utilities")
	static bool CalculateSpriteOccupiedTiles(UPaperSpriteComponent* SpriteComponent, AXwPaperTileMapActor* TileMapActor, TArray<FIntPoint>& OutOccupiedTiles);
	
	/**
	 * 将世界坐标转换为格子坐标
	 * @param WorldLocation 世界坐标
	 * @param TileMapActor 所在的TileMapActor
	 * @param OutTilePosition 输出格子坐标
	 * @return 是否转换成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Utilities")
	static bool WorldToTile(const FVector& WorldLocation, AXwPaperTileMapActor* TileMapActor, FIntPoint& OutTilePosition);
	
	/**
	 * 将格子坐标转换为世界坐标
	 * @param TilePosition 格子坐标
	 * @param TileMapActor 所在的TileMapActor
	 * @param OutWorldLocation 输出世界坐标
	 * @return 是否转换成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Utilities")
	static bool TileToWorld(const FIntPoint& TilePosition, AXwPaperTileMapActor* TileMapActor, FVector& OutWorldLocation);
	
	/**
	 * 向TileMapActor添加动态对象
	 * @param Actor 动态对象
	 * @param TileMapActor 所在的TileMapActor
	 * @param OccupiedTiles 占据的格子
	 * @param TileType 格子类型
	 * @return 是否添加成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Utilities")
	static bool AddDynamicObjectToTileMap(AActor* Actor, AXwPaperTileMapActor* TileMapActor, const TArray<FIntPoint>& OccupiedTiles, ENavigationTileType TileType);
	
	/**
	 * 从TileMapActor移除动态对象
	 * @param Actor 动态对象
	 * @param TileMapActor 所在的TileMapActor
	 * @return 是否移除成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Utilities")
	static bool RemoveDynamicObjectFromTileMap(AActor* Actor, AXwPaperTileMapActor* TileMapActor);
	
private:
	/**
	 * 获取TileMap数据
	 * @param TileMapActor 所在的TileMapActor
	 * @return TileMap数据
	 */
	static TSharedPtr<FGridTileMap> GetTileMapData(AXwPaperTileMapActor* TileMapActor);
}; 