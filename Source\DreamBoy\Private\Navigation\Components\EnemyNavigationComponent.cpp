#include "Navigation/Components/EnemyNavigationComponent.h"
#include "Navigation/TileMap/GridTileMapManager.h"
#include "Navigation/TileMap/XwPaperTileMapActor.h"
#include "Navigation/Algorithms/JumpPointSearch/JumpPointPathFinder.h"
#include "Navigation/Algorithms/JumpPointSearch/JumpPointTileMap.h"
#include "Character/CharacterBase.h"
#include "Character/XwEnemy.h"

UEnemyNavigationComponent::UEnemyNavigationComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	bInitialized = false;
}

void UEnemyNavigationComponent::BeginPlay()
{
	Super::BeginPlay();
	
	// 如果有设置敌人类型，自动初始化
	if (EnemyRace != EEnemyRace::None)
	{
		InitializeNavigation(EnemyRace);
	}
}

void UEnemyNavigationComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
	
	// 注销TileMap
	AXwEnemy* Enemy = Cast<AXwEnemy>(GetOwner());
	if (Enemy && bInitialized)
	{
		AGridTileMapManager* Manager = GetTileMapManager();
		if (Manager)
		{
			Manager->UnRegisterTileMap(Enemy, EnemyRace);
		}
	}
}

bool UEnemyNavigationComponent::InitializeNavigation(EEnemyRace InEnemyRace)
{
	EnemyRace = InEnemyRace;
	
	// 注册TileMap
	AXwEnemy* Enemy = Cast<AXwEnemy>(GetOwner());
	if (!Enemy)
	{
		return false;
	}
	
	AGridTileMapManager* Manager = GetTileMapManager();
	if (!Manager)
	{
		return false;
	}
	
	if (!Manager->RegisterTileMap(Enemy, EnemyRace))
	{
		return false;
	}
	
	// 创建寻路对象
	if (!CreatePathFinder())
	{
		return false;
	}
	
	bInitialized = true;
	return true;
}

TArray<FVector> UEnemyNavigationComponent::FindPath(const FVector& StartWorldPos, const FVector& EndWorldPos)
{
	if (!bInitialized || !PathFinder)
	{
		return TArray<FVector>();
	}
	
	return PathFinder->FindPath(StartWorldPos, EndWorldPos);
}

AXwPaperTileMapActor* UEnemyNavigationComponent::GetCurrentTileMap() const
{
	AGridTileMapManager* Manager = GetTileMapManager();
	if (!Manager)
	{
		return nullptr;
	}
	
	return Manager->FindTileMapByLocation(GetOwner()->GetActorLocation());
}

void UEnemyNavigationComponent::DrawDebug()
{
	if (PathFinder)
	{
		PathFinder->DrawDebug();
	}
}

void UEnemyNavigationComponent::SetShowDebugInfo(bool bShow)
{
	if (PathFinder)
	{
		PathFinder->ShowDebugInfo = bShow;
	}
}

bool UEnemyNavigationComponent::CreatePathFinder()
{
	AXwEnemy* Enemy = Cast<AXwEnemy>(GetOwner());
	if (!Enemy)
	{
		return false;
	}
	
	AGridTileMapManager* Manager = GetTileMapManager();
	if (!Manager)
	{
		return false;
	}
	
	AXwPaperTileMapActor* MapActor = GetCurrentTileMap();
	if (!MapActor)
	{
		return false;
	}
	
	// 获取JumpPointMap
	TSharedPtr<FJumpPointMap> JumpPointMap = Manager->GetJumpPointMap(MapActor, EnemyRace);
	if (!JumpPointMap.IsValid())
	{
		return false;
	}
	
	// 创建寻路对象
	UJumpPointPathFinding* JumpPointPathFinder = NewObject<UJumpPointPathFinding>(this);
	if (!JumpPointPathFinder)
	{
		return false;
	}
	
	JumpPointPathFinder->InitializeWithMap(*JumpPointMap);
	PathFinder = JumpPointPathFinder;
	
	return true;
}

AGridTileMapManager* UEnemyNavigationComponent::GetTileMapManager() const
{
	return AGridTileMapManager::GetManagerInCurrentWorld();
} 