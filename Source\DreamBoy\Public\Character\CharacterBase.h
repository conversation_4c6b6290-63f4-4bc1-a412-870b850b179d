// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PaperZDCharacter.h"
#include "AbilitySystemInterface.h"
#include "Interaction/CombatInterface.h"
#include "GAS/XwAbilitySystemComponent.h"
#include "AnimSequences/PaperZDAnimSequence.h"
#include "Delegates/DelegateCombinations.h"

#include "CharacterBase.generated.h"

class UAttributeSet;
class UBoxComponent;
class UXwCharacterConfigAsset;
class UAStarPathFindingBase;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FAttributeChangedMulticastDelegate, const FGameplayAttribute&, Attribute, float, OldValue, float, NewValue);


UENUM(BlueprintType)
enum class EEnemyType : uint8
{
	Normal,
	Elite,
	Boss
};

UENUM(BlueprintType)
enum class EGroundType : uint8
{
	Block,
	Crossable,
	SkillGenerate,
	Air
};

USTRUCT(BlueprintType)
struct DREAMBOY_API FAnimateAbilityPair
{
	GENERATED_USTRUCT_BODY()

	FAnimateAbilityPair(){}
	FAnimateAbilityPair(TSubclassOf<UGameplayAbility> Class, TObjectPtr<UPaperZDAnimSequence> SQ)
		: AbilityClass(Class), Sequence(SQ)
	{}

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TSubclassOf<UGameplayAbility> AbilityClass;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, AssetRegistrySearchable)
	TObjectPtr<UPaperZDAnimSequence> Sequence;
};

/**
 * 
 */
UCLASS()
class DREAMBOY_API ACharacterBase : public APaperZDCharacter, public IAbilitySystemInterface, public ICombatInterface
{
	GENERATED_BODY()
	
public:
	/*--------	AbilitySystem Interface Start	--------*/
	virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;
	/*--------	AbilitySystem Interface End		--------*/

public:
	ACharacterBase();

	UXwCharacterConfigAsset* GetConfigAsset() { return ConfigAsset; }

	virtual void Tick(float DeltaSeconds) override;

protected:
	virtual void OnConstruction(const FTransform& Transform) override;

	virtual bool LoadConfigAsset();
	virtual void InitReferences() {};
	virtual void InitAbilities();
	virtual void InitializeDefaultAttributes() const;
	void ApplyAttributeInitEffectToSelf(TSubclassOf<UGameplayEffect> AttributeInitEffectClass, float Level) const;

private:
	void GiveAbility_Internal(TSubclassOf<UGameplayAbility> AbilityClass, UPaperZDAnimSequence* AbilityAnimSQ = nullptr, int32 AbilityLevel = 1);
protected:
	UFUNCTION()
	void OnEdgeBeginOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	UFUNCTION()
	void OnEdgeEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
	UFUNCTION()
	void OnWallBeginOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	UFUNCTION()
	void OnWallEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
	UFUNCTION()
	void OnGroundBeginOverlap(UPrimitiveComponent* OverlappedComp, AActor* Other, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	UFUNCTION()
	void OnGroundEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
	
public:
	UFUNCTION(BlueprintCallable, Category = "Ability System")
	void GiveAbility(TSubclassOf<UGameplayAbility> AbilityClass, UPaperZDAnimSequence* AbilityAnimSQ = nullptr, int32 AbilityLevel = 1);
	UFUNCTION(BlueprintCallable, Category = "Ability System")
	bool GetActiveAbilityHandleOfClass(TSubclassOf<UGameplayAbility> InAbilityToActivate, FGameplayAbilitySpecHandle& OutHandle);
	UFUNCTION(BlueprintCallable, Category = "Ability System")
	void CancelAbilitiesByHandle(const FGameplayAbilitySpecHandle& Handle);
	UFUNCTION(BlueprintCallable, Category = "Ability System")
	void CancelAbilitiesByClass(TSubclassOf<UGameplayAbility> InAbilityToActivate);
	UFUNCTION(BlueprintCallable, Category = "Ability System")
	void CancelAbilitiesByTag(const FGameplayTagContainer& GameplayTagContainer);
	UFUNCTION(BlueprintCallable, Category = "Ability System")
	void CancelAllAbilities(UGameplayAbility* Ignore = nullptr);


	const UPaperZDAnimSequence* GetConfiguredAnimSQ_ByAbilityTagName(FName TagName) const;
	UFUNCTION(BlueprintCallable)
	FVector GetFootLocation();
	UFUNCTION(BlueprintCallable)
	void AdjustDetectors();
	UFUNCTION(BlueprintCallable)
	bool IsCloseToEdge() { return EdgeDetect_OverlappedActor.IsEmpty(); };
	UFUNCTION(BlueprintCallable)
	bool IsCloseToWall() { return !WallDetect_OverlappedActor.IsEmpty(); };
	UFUNCTION(BlueprintCallable)
	bool IsSafeToWalkDownEdge();
	UFUNCTION(BlueprintCallable)
	bool CanJumpToLocation(FVector TargetLocationInWorld);
	UFUNCTION(BlueprintCallable)
	EGroundType GetGroundTypeStandingOn();

	UFUNCTION(BlueprintCallable)
	TArray<FVector> CalculateJumpTrajectory(float TimeInterval = 0.03);

	UPROPERTY(BlueprintAssignable)
	FAttributeChangedMulticastDelegate OnAttributeChanged;

public:
	UPROPERTY()
	TObjectPtr<UAStarPathFindingBase> PathFindObject;
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, meta = (AllowPrivateAccess = "true"))
	UBoxComponent* EdgeDetector;
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, meta = (AllowPrivateAccess = "true"))
	UBoxComponent* WallDetector;
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, meta = (AllowPrivateAccess = "true"))
	UBoxComponent* GroundDetector;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = GameplayAbilities, meta = (AllowPrivateAccess = "true"))
	UAbilitySystemComponent* AbilitySystemComponent;
	UPROPERTY()
	UAttributeSet* AttributeSet;

	UPROPERTY(BlueprintReadOnly, Category = "Attributes")
	TSubclassOf<UGameplayEffect> PrimaryInitEffectClass;
	UPROPERTY(BlueprintReadOnly, Category = "Attributes")
	TSubclassOf<UGameplayEffect> SecondaryInitEffectClass;
	UPROPERTY(BlueprintReadOnly, Category = "Attributes")
	TSubclassOf<UGameplayEffect> VitalInitEffectClass;


//Temp
public:
	UFUNCTION(BlueprintCallable)
	void PrepareForProjectile(TSubclassOf<AActor> Class) { ProjectileClass = Class; }
	UFUNCTION(BlueprintCallable)
	TSubclassOf<AActor> ShootProjectile() { TSubclassOf<AActor> Cache = ProjectileClass; ProjectileClass = nullptr; return Cache; }
	UFUNCTION(BlueprintCallable)
	void HandleAttack();
	UFUNCTION(BlueprintCallable)
	void PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue);
protected:
	TSubclassOf<AActor>	ProjectileClass;

protected:
	UPROPERTY(EditDefaultsOnly, Category = "RequiredSettings")
	UXwCharacterConfigAsset* ConfigAsset;

public:
	//UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = RequiredSettings)
	//TMap<FGameplayTag, UPaperZDAnimSequence*> Ability_AnimSQ_Map;

	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* IdleAnimSQ;
	//UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = RequiredSettings)
	//UPaperZDAnimSequence MoveAnimSQ;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* RunAnimSQ;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* StopAnimSQ;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* JumpStartAnimSQ;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* JumpingAnimSQ;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* FallingAnimSQ;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* GuardAnimSQ;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	UPaperZDAnimSequence* DeadAnimSQ;

	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	FAnimateAbilityPair DashConfig;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	TArray<FAnimateAbilityPair> ComboConfig;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	FAnimateAbilityPair ChargeConfig;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	TArray<FAnimateAbilityPair> SkillConfig;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	FAnimateAbilityPair ItemConfig;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	FAnimateAbilityPair HitConfig;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	FAnimateAbilityPair KnockBackConfig;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	FAnimateAbilityPair DeadConfig;

	//UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = RequiredSettings)
	//TMap<FName, FAnimateAbilityPair> RequiredAbilityConfigs;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	TArray<FAnimateAbilityPair> AdditionalAbilityConfigs;
	UPROPERTY(BlueprintReadOnly, Category = RequiredSettings)
	TArray<TSubclassOf<UGameplayAbility>> PassiveAbilityConfigs;

private:
	TMap<FName, FAnimateAbilityPair> CachedAbilityConfigs;
protected:
	TArray<AActor*> EdgeDetect_OverlappedActor;
	TArray<AActor*> WallDetect_OverlappedActor;
	UPROPERTY(BlueprintReadOnly)
	TArray<AActor*> GroundDetect_OverlappedActor;
};
