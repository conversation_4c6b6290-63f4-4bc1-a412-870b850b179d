// Fill out your copyright notice in the Description page of Project Settings.

#include "GAS/XwAbilitySystemLibrary.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Character/CharacterBase.h"
#include "UI/HUD/XwHUD.h"
#include "UI/WidgetController/OverlayWidgetController.h"
#include "UI/WidgetController/AttributeMenuWidgetController.h"
#include "UI/WidgetController/CharacterBuildWidgetController.h"
#include "Player/XwPlayerController.h"
#include "Player/XwPlayerState.h"
#include "XwGameplayEffectContext.h"
#include "GAS/XwAbilitySystemComponent.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "XwGameplayTags.h"

float UXwAbilitySystemLibrary::GetCharacterAttributeByTag(const UObject *WorldContextObject, ACharacterBase *Character, FGameplayTag Tag)
{
	if (!Character)
		return 0.0;
	UAttributeSetBase *AS = Cast<UAttributeSetBase>(Character->AttributeSet);
	if (!AS)
		return 0.0;
	if (auto Func = AS->TagsToAttributes.Find(Tag))
	{
		FGameplayAttribute Attribute = (*Func)();
		return Attribute.GetNumericValue(AS);
	}
	return 0.0;
}

int UXwAbilitySystemLibrary::GetCharacterLevel(const UObject* WorldContextObject, AActor* Character)
{
	ACharacterBase* XwCharacter = Cast<ACharacterBase>(Character);
	if (!XwCharacter) return 0;
	return XwCharacter->GetPlayerLevel();
}

bool UXwAbilitySystemLibrary::MakeWidgetControllerParams(const UObject *WorldContextObject, FWidgetControllerParams &OutWCParams, AXwHUD *&OutXwHUD)
{
	if (APlayerController *PC = UGameplayStatics::GetPlayerController(WorldContextObject, 0))
	{
		if (AXwHUD *XwHUD = Cast<AXwHUD>(PC->GetHUD()))
		{
			AXwPlayerState *PS = PC->GetPlayerState<AXwPlayerState>();
			UAbilitySystemComponent *ASC = PS->GetAbilitySystemComponent();
			UAttributeSet *AS = PS->GetAttributeSet();

			OutWCParams.AS = AS;
			OutWCParams.PS = PS;
			OutWCParams.PC = PC;
			OutWCParams.ASC = ASC;

			OutXwHUD = XwHUD;
			return true;
		}
	}
	return false;
}

UOverlayWidgetController *UXwAbilitySystemLibrary::GetOverlayWidgetController(const UObject *WorldContextObject)
{
	FWidgetControllerParams WidgetControllerParams;
	AXwHUD *XwHUD = nullptr;

	if (!MakeWidgetControllerParams(WorldContextObject, WidgetControllerParams, XwHUD))
	{
		return nullptr;
	}

	return XwHUD->GetOverlayWidgetController(WidgetControllerParams);
}

UAttributeMenuWidgetController *UXwAbilitySystemLibrary::GetAttributeMenuWidgetController(const UObject *WorldContextObject)
{
	FWidgetControllerParams WidgetControllerParams;
	AXwHUD *XwHUD = nullptr;

	if (!MakeWidgetControllerParams(WorldContextObject, WidgetControllerParams, XwHUD))
	{
		return nullptr;
	}

	return XwHUD->GetAttributeMenuWidgetController(WidgetControllerParams);
}

UCharacterBuildWidgetController *UXwAbilitySystemLibrary::GetCharacterBuildWidgetController(const UObject *WorldContextObject)
{
	FWidgetControllerParams WidgetControllerParams;
	AXwHUD *XwHUD = nullptr;

	if (!MakeWidgetControllerParams(WorldContextObject, WidgetControllerParams, XwHUD))
	{
		return nullptr;
	}

	return XwHUD->GetCharacterBuildWidgetController(WidgetControllerParams);
}

FGameplayEffectContextHandle UXwAbilitySystemLibrary::ApplyDamageEffect(const FDamageEffectParams &DamageEffectParams)
{
	const AActor *SourceAvatarActor = DamageEffectParams.SourceAbilitySystemComponent->GetAvatarActor();

	FGameplayEffectContextHandle EffectContextHandle = DamageEffectParams.SourceAbilitySystemComponent->MakeEffectContext();
	EffectContextHandle.AddSourceObject(SourceAvatarActor);
	// SetDeathImpulse(EffectContextHandle, DamageEffectParams.DeathImpulse);
	// SetKnockbackForce(EffectContextHandle, DamageEffectParams.KnockbackForce);

	////Set Radial Damage Param.
	// SetIsRadialDamage(EffectContextHandle, DamageEffectParams.bIsRadialDamage);
	// SetRadialDamageOrigin(EffectContextHandle, DamageEffectParams.RadialDamageOrigin);
	// SetRadialDamageInnerRadius(EffectContextHandle, DamageEffectParams.RadialDamageInnerRadius);
	// SetRadialDamageOuterRadius(EffectContextHandle, DamageEffectParams.RadialDamageOuterRadius);

	const FGameplayEffectSpecHandle SpecHandle = DamageEffectParams.SourceAbilitySystemComponent->MakeOutgoingSpec(
		DamageEffectParams.DamageGameplayEffectClass,
		DamageEffectParams.AbilityLevel,
		EffectContextHandle);

	const FXwGameplayTags &GameplayTags = FXwGameplayTags::Get();

	// Set By Caller
	UAbilitySystemBlueprintLibrary::AssignTagSetByCallerMagnitude(SpecHandle,
																  DamageEffectParams.DamageType, DamageEffectParams.BaseDamage);

	// UAbilitySystemBlueprintLibrary::AssignTagSetByCallerMagnitude(SpecHandle,
	//	GameplayTags.Debuff_Chance, DamageEffectParams.DebuffChance);

	// UAbilitySystemBlueprintLibrary::AssignTagSetByCallerMagnitude(SpecHandle,
	//	GameplayTags.Debuff_Damage, DamageEffectParams.DebuffDamage);

	// UAbilitySystemBlueprintLibrary::AssignTagSetByCallerMagnitude(SpecHandle,
	//	GameplayTags.Debuff_Duration, DamageEffectParams.DebuffDuration);

	// UAbilitySystemBlueprintLibrary::AssignTagSetByCallerMagnitude(SpecHandle,
	//	GameplayTags.Debuff_Frequency, DamageEffectParams.DebuffFrequency);

	DamageEffectParams.TargetAbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data);

	return EffectContextHandle;
}
