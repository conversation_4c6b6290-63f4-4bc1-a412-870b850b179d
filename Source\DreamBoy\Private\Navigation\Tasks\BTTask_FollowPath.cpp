#include "Navigation/Tasks/BTTask_FollowPath.h"
#include "Navigation/Tasks/AITask_NavigateTo.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "AIController.h"
#include "Character/XwEnemy.h"
#include "Navigation/Components/EnemyNavigationComponent.h"

UBTTask_FollowPath::UBTTask_FollowPath()
{
	NodeName = "Follow Path";
	//bNotifyTick = true; // 启用Tick
	
	// 设置黑板键选择器接受位置类型
	TargetLocationKey.AddVectorFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_FollowPath, TargetLocationKey));
	
	// 初始化变量
	PathBlockedRetries = 0;
	bNotifyTick = true;
}

EBTNodeResult::Type UBTTask_FollowPath::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	BTComponent = &OwnerComp;
	
	// 获取AI控制器
	AAIController* AIController = OwnerComp.GetAIOwner();
	if (!AIController)
	{
		return EBTNodeResult::Failed;
	}
	
	// 获取黑板组件
	BlackboardComp = OwnerComp.GetBlackboardComponent();
	if (!BlackboardComp)
	{
		return EBTNodeResult::Failed;
	}
	
	// 获取敌人
	AXwEnemy* Enemy = Cast<AXwEnemy>(AIController->GetPawn());
	if (!Enemy)
	{
		return EBTNodeResult::Failed;
	}
	
	// 检查导航组件和寻路对象
	UEnemyNavigationComponent* NavComp = Enemy->GetNavigationComponent();
	if (!NavComp || !NavComp->GetPathFinder())
	{
		return EBTNodeResult::Failed;
	}
	
	// 获取目标位置
	CurrentTargetLocation = BlackboardComp->GetValueAsVector(TargetLocationKey.SelectedKeyName);
	// 获取追踪目标
	CurrentFollowTarget = Cast<AActor>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
	
	// 创建导航请求
	FAINavigateRequest Request;
	Request.StartWolrdLocation = Enemy->GetFootLocation();
	Request.EndWorldLocation = CurrentTargetLocation;
	Request.FollowTarget = CurrentFollowTarget;
	Request.NavComponent = NavComp;
	Request.ShowDebugInfo = bShowDebugInfo;	

	Request.bCheckTimeout = bCheckTimeout;
	Request.TimeoutSeconds = TimeoutSeconds;
	Request.bRepathWhenTimeout = bRepathWhenTimeout;

	Request.bCheckTargetMove = bCheckTargetMove;
	Request.TargetMoveTolerance = TargetMoveTolerance;
	Request.bRepathWhenTargetMoves = bRepathWhenTargetMoves;

	Request.bCheckStuck = bCheckStuck;
	Request.StuckCheckTime = StuckCheckTime;
	Request.StuckDistance = StuckDistance;
	Request.bRepathWhenStuck = bRepathWhenStuck;

	Request.bCheckAttackRange = bCheckAttackRange;
	Request.AttackRange = AttackRange;
	Request.bFinishInAttackRange = bFinishInAttackRange;
	
	// 创建导航任务
	NavigationTask = UAITask_NavigateTo::NavigateTo(AIController, Request);
	if (!NavigationTask)
	{
		return EBTNodeResult::Failed;
	}
	
	// 绑定完成事件
	NavigationTask->OnNavigationCompleted.AddDynamic(this, &UBTTask_FollowPath::OnNavigationCompleted);
	
	// 绑定其他事件
	NavigationTask->OnTargetMoved.AddDynamic(this, &UBTTask_FollowPath::OnTargetMoved);
	NavigationTask->OnStuck.AddDynamic(this, &UBTTask_FollowPath::OnStuck);
	NavigationTask->OnEnterAttackRange.AddDynamic(this, &UBTTask_FollowPath::OnEnterAttackRange);
	NavigationTask->OnPathBlocked.AddDynamic(this, &UBTTask_FollowPath::OnPathBlocked);
	
	// 重置路径阻塞尝试次数
	PathBlockedRetries = 0;
	
	// 激活任务
	NavigationTask->ReadyForActivation();
	
	return EBTNodeResult::InProgress;
}

void UBTTask_FollowPath::TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
	if (!NavigationTask || NavigationTask->GetState() == EGameplayTaskState::Finished) {
		FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
		NavigationTask = nullptr;
	}
}

EBTNodeResult::Type UBTTask_FollowPath::AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	if (NavigationTask)
	{
		NavigationTask->EndTask();
		NavigationTask = nullptr;
	}
	
	return EBTNodeResult::Aborted;
}

FString UBTTask_FollowPath::GetStaticDescription() const
{
	FString Description = FString::Printf(TEXT("沿着路径移动到目标位置"));
	
	if (bCheckTargetMove)
	{
		Description += FString::Printf(TEXT("\n检查目标移动: 是, 容忍距离: %.1f"), TargetMoveTolerance);
	}
	
	if (bCheckStuck)
	{
		Description += FString::Printf(TEXT("\n检查卡住: 是, 检测时间: %.1f秒, 检测距离: %.1f"), StuckCheckTime, StuckDistance);
	}
	
	if (bCheckAttackRange)
	{
		Description += FString::Printf(TEXT("\n检查攻击范围: 是, 攻击范围: %.1f"), AttackRange);
	}
	
	if (TimeoutSeconds > 0.0f)
	{
		Description += FString::Printf(TEXT("\n超时时间: %.1f秒"), TimeoutSeconds);
	}
	
	return Description;
}

void UBTTask_FollowPath::OnNavigationCompleted(bool bSuccess, ENavigationTaskResult Result)
{
	if (!BTComponent)
	{
		return;
	}
	
	// 根据导航结果完成任务
	EBTNodeResult::Type NodeResult;
	
	if (bSuccess)
	{
		switch (Result)
		{
		case ENavigationTaskResult::Success:
			NodeResult = EBTNodeResult::Succeeded;
			break;
		case ENavigationTaskResult::InAttackRange:
			// 如果配置为在攻击范围内结束任务，则返回成功
			NodeResult = bFinishInAttackRange ? EBTNodeResult::Succeeded : EBTNodeResult::InProgress;
			break;
		default:
			NodeResult = EBTNodeResult::Succeeded;
			break;
		}
	}
	else
	{
		switch (Result)
		{
		case ENavigationTaskResult::Failed:
			NodeResult = EBTNodeResult::Failed;
			break;
		case ENavigationTaskResult::InvalidPath:
			NodeResult = EBTNodeResult::Failed;
			break;
		case ENavigationTaskResult::Stuck:
			// 如果配置为在卡住时重新寻路，任务还在进行中
			NodeResult = bRepathWhenStuck ? EBTNodeResult::InProgress : EBTNodeResult::Failed;
			break;
		case ENavigationTaskResult::Timeout:
			NodeResult = EBTNodeResult::Failed;
			break;
		case ENavigationTaskResult::TargetMoved:
			// 如果配置为在目标移动时重新寻路，任务还在进行中
			NodeResult = bRepathWhenTargetMoves ? EBTNodeResult::InProgress : EBTNodeResult::Failed;
			break;
		default:
			NodeResult = EBTNodeResult::Failed;
			break;
		}
	}
	
	// 如果任务已经完成，则清理资源
	if (NodeResult != EBTNodeResult::InProgress)
	{
		FinishLatentTask(*BTComponent, NodeResult);
		NavigationTask = nullptr;
	}
}

void UBTTask_FollowPath::OnTargetMoved()
{
	
}

void UBTTask_FollowPath::OnStuck()
{
	if (!bRepathWhenStuck)
	{
		// 否则，结束任务
		FinishLatentTask(*BTComponent, EBTNodeResult::Failed);
		NavigationTask = nullptr;
	}
}

void UBTTask_FollowPath::OnEnterAttackRange()
{
	if (!BTComponent || !NavigationTask)
	{
		return;
	}
	
	// 如果配置为在攻击范围内结束任务，则结束任务
	if (bFinishInAttackRange)
	{
		FinishLatentTask(*BTComponent, EBTNodeResult::Succeeded);
		NavigationTask = nullptr;
	}
}

void UBTTask_FollowPath::OnPathBlocked()
{
	if (!BTComponent || !NavigationTask)
	{
		return;
	}
		
	// 递增路径阻塞尝试次数
	PathBlockedRetries++;
	
	// 如果尝试次数超过最大值，则结束任务
	if (PathBlockedRetries >= MaxPathBlockedRetries)
	{
		FinishLatentTask(*BTComponent, EBTNodeResult::Failed);
		NavigationTask = nullptr;
		return;
	}
	
	// 否则，尝试重新寻路
	//NavigationTask->UpdateTargetLocation(NavigationTask->GetTargetLocation());
	NavigationTask->ReStartFromCurrentLocation();
} 