// Fill out your copyright notice in the Description page of Project Settings.


#include "GAS/MMC/Attribute/MMC_Attribute_MaxHealth.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "Interaction/CombatInterface.h"
#include "Character/CharacterBase.h"
#include "Data/XwCharacterConfigAsset.h"

UMMC_Attribute_MaxHealth::UMMC_Attribute_MaxHealth()
{
	VitalityDef.AttributeToCapture = UAttributeSetBase::GetVitalityAttribute();
	VitalityDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
	VitalityDef.bSnapshot = false;

	RelevantAttributesToCapture.Add(VitalityDef);
}

float UMMC_Attribute_MaxHealth::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	// Gather tags from source and target
	const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
	const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluationParameters;
	EvaluationParameters.SourceTags = SourceTags;
	EvaluationParameters.TargetTags = TargetTags;

	float Vitality = 0.f;
	GetCapturedAttributeMagnitude(VitalityDef, Spec, EvaluationParameters, Vitality);
	Vitality = FMath::Max<float>(Vitality, 0.f);

	//int32 PlayerLevel = 1;
	//if (Spec.GetContext().GetSourceObject()->Implements<UCombatInterface>())
	//{
	//	PlayerLevel = ICombatInterface::Execute_GetPlayerLevel(Spec.GetContext().GetSourceObject());
	//}
	float BasicValue = 0.0;
	if (ACharacterBase* Character = Cast<ACharacterBase>(Spec.GetContext().GetSourceObject()))
	{
		if (UXwCharacterConfigAsset* ConfigAsset = Character->GetConfigAsset())
		{
			BasicValue = ConfigAsset->BasicValue_MaxHealth;
		}
	}
	

	return BasicValue + 5.f * Vitality;
}
