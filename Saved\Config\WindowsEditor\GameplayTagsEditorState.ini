[GameplayTagPicker]
State.Item.Expanded=False
State.Expanded=True
State.Attack.Expanded=False
Ability.Expanded=False
Ability.Jump.Expanded=False
Ability.Attack.Expanded=False
State.Attack.Combo.1.Expanded=False
State.Attack.Combo.Expanded=True
State.Attack.Combo.2.Expanded=False
State.Attack.Combo.3.Expanded=False
State.InputCache.NormalAttack.Expanded=True
State.InputCache.Expanded=True
ManagerState.Expanded=True
ManagerState.InputCache.Expanded=True
State.Ready.Combo.2.Expanded=False
State.Ready.Combo.Expanded=True
State.Ready.Expanded=True
Edit.Expanded=True
State.Ready.Combo.3.Expanded=True
State.NextAttack.Expanded=True
State.Input.Reactionable.Expanded=True
State.Input.Expanded=True
Block.Input.NormalAttack.Expanded=True
Block.Input.Expanded=True
Block.Expanded=False
Status.Expanded=False
ManagerTests.Expanded=False
ManagerStatus.Expanded=False
ManagerState.Ready.Expanded=True
ManagerState.Ready.Combo.Expanded=True
State.Dash.Expanded=False
Ability.Dash.Expanded=True
ManagerState.Dash.Immune.Expanded=True
ManagerState.Dash.Expanded=False
State.Enable.Dash.Expanded=True
State.Enable.Expanded=False
State.Enable.Jump.Expanded=True
CoolDown.Dash.Expanded=True
CoolDown.Expanded=True
ManagerState.Input.Expanded=True
ManagerInputWindow.All.Expanded=True
ManagerInputWindow.Expanded=False
ManagerInputWindow.Dash.Expanded=True
ManagerInputWindow.Jump.Expanded=True
ManagerInputWindow.Move.Expanded=True
ManagerInputWindow.Skill.Expanded=True
ManagerInputWindow.Item.Expanded=True
InputWindow.Attack.Expanded=True
InputWindow.Expanded=False
Block.Input.All.Expanded=True
ManagerBlock.Expanded=True
ManagerBlock.Input.Expanded=True
Block.Input.Dash.Expanded=True
ManagerBlock.Input.Jump.Expanded=True
ManagerBlock.Input.Item.Expanded=True
ManagerBlock.Input.Combo.1.Expanded=True
ManagerBlock.Input.Combo.Expanded=False
ManagerBlock.Input.Combo.2.Expanded=True
ManagerBlock.Input.Combo.3.Expanded=True
ManagerBlock.Input.Move.Expanded=True
ManagerBlock.Input.Skill.Expanded=True
Ability.Hit.Expanded=False
ManagerAbility.Expanded=False
ManagerAbility.KnockBack.Expanded=True
ManagerAttribute.Expanded=True
ManagerAttribute.AttackPower.Expanded=False
Attribute.Expanded=False
ManagerEvent.Hit.Expanded=True
ManagerEvent.Expanded=True
Event.Expanded=False
ManagerEvent.KnockBack.Expanded=True
ManagerState.FoundTarget.BySight.Expanded=True
ManagerState.FoundTarget.Expanded=True
ManagerState.FoundTarget.ByHear.Expanded=True
ManagerState.FoundTarget.ByDamage.Expanded=True
State.FoundTarget.Expanded=False
Ability.Attack.Normal.Expanded=True
ManagerAbility.Attack.Expanded=False
ManagerAbility.Attack.Special.Expanded=False
ManagerAbility.Attack.Normal.Expanded=False
ManagerAbility.Skill.ThrowHeart.Expanded=True
ManagerAbility.Skill.Expanded=True
Ability.Skill.Expanded=True
GameplayCue.Expanded=True
ManagerState.PefectGuard.Expanded=True
ManagerAttribute.Mana.Expanded=True
ManagerBlock.Input.All.Expanded=True
Block.Input.Combo.Expanded=False
ManagerEvent.Falling.Expanded=True
ManagerAbility.WrathCombo.1.Expanded=True
ManagerAbility.WrathCombo.Expanded=True
ManagerAbility.WrathCombo.2.Expanded=True
ManagerAbility.WrathCombo.3.Expanded=True
ManagerState.WrathCombo.1.Expanded=True
ManagerState.WrathCombo.Expanded=True
ManagerState.WrathCombo.2.Expanded=True
ManagerState.WrathCombo.3.Expanded=True
ManagerAbility.Skill.WrathBuff.Expanded=True
ManagerState.Buff.AttackSpeed.Expanded=True
ManagerState.Buff.Expanded=True
ManagerAbility.Skill.PrideBurst.Expanded=True
CoolDown.Skill.Expanded=True
ManagerAttributes.Expanded=True
Attributes.Expanded=True
Attributes.Primary.Expanded=False
Attributes.Secondary.Expanded=False
GameplayCue.DamageText.Expanded=True
Attributes.Meta.Expanded=True

