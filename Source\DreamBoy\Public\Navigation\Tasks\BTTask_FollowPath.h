#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "Navigation/Tasks/AITask_NavigateTo.h"
#include "BTTask_FollowPath.generated.h"

//struct FBTFollowPathTaskMemory
//{
//	/** Move request ID */
//	FAIRequestID MoveRequestID;
//
//	FDelegateHandle BBObserverDelegateHandle;
//	FVector PreviousGoalLocation;
//
//	TWeakObjectPtr<UAITask_NavigateTo> ActiveTask;
//
//	uint8 bObserverCanFinishTask : 1;
//};

/**
 * 行为树任务：沿着路径移动
 * 使用我们的自定义导航系统
 */
UCLASS()
class DREAMBOY_API UBTTask_FollowPath : public UBTTaskNode
{
	GENERATED_BODY()
	
public:
	UBTTask_FollowPath();

	/** 初始化任务 */
	virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;

	virtual void TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;

	/** 结束任务 */
	virtual EBTNodeResult::Type AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;

	///** 任务大小 */
	//virtual uint16 GetInstanceMemorySize() const override {
	//	return sizeof(FBTFollowPathTaskMemory);
	//}

	/** 任务描述 */
	virtual FString GetStaticDescription() const override;

protected:
	/** 导航完成回调 */
	UFUNCTION()
	void OnNavigationCompleted(bool bSuccess, ENavigationTaskResult Result);

	/** 目标移动回调 */
	UFUNCTION()
	void OnTargetMoved();

	/** 卡住回调 */
	UFUNCTION()
	void OnStuck();

	/** 进入攻击范围回调 */
	UFUNCTION()
	void OnEnterAttackRange();

	/** 路径阻塞回调 */
	UFUNCTION()
	void OnPathBlocked();

protected:
	/** 目标位置键 */
	UPROPERTY(EditAnywhere, Category = "Blackboard")
	FBlackboardKeySelector TargetLocationKey;
	/** 追踪目标 */
	UPROPERTY(EditAnywhere, Category = "Blackboard")
	FBlackboardKeySelector TargetActorKey;

	/** 当路径阻塞时尝试的最大重新寻路次数 */
	UPROPERTY(EditAnywhere, Category = "Navigation")
	int32 MaxPathBlockedRetries = 3;

	/** 定时重新导航 */
	UPROPERTY(EditAnywhere, Category = "Navigation")
	bool bCheckTimeout = false;
	/** 导航超时时间 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckTimeout"))
	float TimeoutSeconds = 10.0f;
	/** 超时重新寻路 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckTimeout"))
	bool bRepathWhenTimeout = true;

	/** 是否检查目标移动 */
	UPROPERTY(EditAnywhere, Category = "Navigation")
	bool bCheckTargetMove = true;
	/** 目标移动容忍距离 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckTargetMove"))
	float TargetMoveTolerance = 100.0f;
	/** 当目标移动时重新寻路 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckTargetMove"))
	bool bRepathWhenTargetMoves = true;

	/** 是否检查卡住 */
	UPROPERTY(EditAnywhere, Category = "Navigation")
	bool bCheckStuck = true;
	/** 卡住检测时间 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckStuck"))
	float StuckCheckTime = 1.0f;
	/** 卡住检测距离 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckStuck"))
	float StuckDistance = 10.0f;
	/** 当卡住时重新寻路 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckStuck"))
	bool bRepathWhenStuck = true;

	/** 是否检查攻击范围 */
	UPROPERTY(EditAnywhere, Category = "Navigation")
	bool bCheckAttackRange = false;
	/** 攻击范围 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckAttackRange"))
	float AttackRange = 150.0f;
	/** 在攻击范围内结束任务 */
	UPROPERTY(EditAnywhere, Category = "Navigation", meta = (EditCondition = "bCheckAttackRange"))
	bool bFinishInAttackRange = true;


	/** 是否显示调试信息 */
	UPROPERTY(EditAnywhere, Category = "Navigation")
	bool bShowDebugInfo = false;
private:
	/** 导航任务 */
	UPROPERTY()
	TObjectPtr<UAITask_NavigateTo> NavigationTask;

	/** 行为树组件 */
	UBehaviorTreeComponent* BTComponent;

	/** 黑板组件 */
	UBlackboardComponent* BlackboardComp;

	/** 当前目标位置 */
	FVector CurrentTargetLocation;

	/** 当前目标位置 */
	AActor* CurrentFollowTarget;

	/** 路径阻塞尝试次数 */
	int32 PathBlockedRetries;
}; 