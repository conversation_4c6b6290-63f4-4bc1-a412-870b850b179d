#include "Navigation/Algorithms/ColumnAccelerate/ColumnAccTypes.h"

bool FPathFindingColumn::Contains(const FPathFindingColumn& Other)
{
	return Bottom <= Other.Bottom && Top >= Other.Top;
}

bool FPathFindingColumn::Touch(const FPathFindingColumn& Other, int Tolerance)
{
	if (Top < Other.Bottom || Other.Top < Bottom) return false;
	int MinTop = FMath::Min(Top, Other.Top);
	int MaxBottom = FMath::Max(Bottom, Other.Bottom);
	return MinTop - MaxBottom + 1 >= Tolerance;
}
