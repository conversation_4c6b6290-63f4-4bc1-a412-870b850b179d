#pragma once

#include "CoreMinimal.h"
#include "AITypes.h"
#include "Tasks/AITask.h"
#include "Navigation/Core/XwNavTypes.h"
#include "AITask_NavigateTo.generated.h"

class UAStarPathFindingBase;
class UCharacterMovementComponent;
class UEnemyNavigationComponent;

/**
 * 导航任务结果枚举
 */
UENUM(BlueprintType)
enum class ENavigationTaskResult : uint8
{
	Success,            // 成功到达目标点
	Failed,             // 导航失败
	Interrupted,        // 导航被中断
	Blocked,            // 路径被阻挡
	Timeout,            // 导航超时
	InvalidPath,        // 无效路径
	PathChanged,        // 路径发生变化
	TargetMoved,        // 目标移动
	Stuck,              // 卡住
	OutOfRange,         // 超出范围
	InAttackRange       // 进入攻击范围
};

/**
 * 导航请求参数
 */
USTRUCT(BlueprintType)
struct FAINavigateRequest
{
	GENERATED_BODY()

	/** 起点 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	FVector StartWolrdLocation;
	/** 终点 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	FVector EndWorldLocation;
	/** 追踪的目标 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	AActor* FollowTarget = nullptr;

	/** 寻路对象 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	UEnemyNavigationComponent* NavComponent = nullptr;


	/** 是否检查任务超时 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bCheckTimeout = false;
	/** 导航超时时间（秒），如果为0则不超时 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	float TimeoutSeconds = 0.0f;
	/** 超时重新寻路 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bRepathWhenTimeout = true;

	/** 是否检查目标移动 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bCheckTargetMove = false;
	/** 目标移动容忍距离，超过此距离认为目标已移动 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	float TargetMoveTolerance = 100.0f;
	/** 当目标移动时重新寻路 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bRepathWhenTargetMoves = true;

	/** 是否检查是否卡住 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bCheckStuck = true;
	/** 卡住检测时间（秒） */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	float StuckCheckTime = 1.0f;
	/** 卡住检测距离，在检测时间内移动距离小于此值则认为卡住 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	float StuckDistance = 10.0f;
	/** 当卡住时重新寻路 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bRepathWhenStuck = true;

	/** 是否检查攻击范围 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bCheckAttackRange = false;
	/** 攻击范围 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	float AttackRange = 150.0f;
	/** 在攻击范围内结束任务 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bFinishInAttackRange = true;


	/** 是否显示调试信息 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool ShowDebugInfo = false;
};

/**
 * 导航任务完成事件
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnNavigationCompleted, bool, bSuccess, ENavigationTaskResult, Result);

/**
 * AI导航任务
 * 用于控制AI角色沿着路径移动
 */
UCLASS()
class DREAMBOY_API UAITask_NavigateTo : public UAITask
{
	GENERATED_BODY()
	
public:
	/** 导航完成事件 */
	UPROPERTY(BlueprintAssignable)
	FOnNavigationCompleted OnNavigationCompleted;

	/**
	 * 创建导航任务
	 * @param Controller AI控制器
	 * @param InRequest 导航请求参数
	 * @return 导航任务
	 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks", meta = (DefaultToSelf = "Controller", BlueprintInternalUseOnly = "TRUE"))
	static UAITask_NavigateTo* NavigateTo(AAIController* Controller, const FAINavigateRequest& InRequest);

	/**
	 * 设置导航任务参数
	 * @param Controller AI控制器
	 * @param InRequest 导航请求参数
	 */
	void SetUp(AAIController* Controller, const FAINavigateRequest& InRequest);

	/** 激活任务 */
	virtual void Activate() override;

	/** 暂停任务 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	void PauseTask();

	/** 恢复任务 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	void ResumeTask();
	
	/**
	 * 更新目标位置
	 * @param NewTargetLocation 新的目标位置
	 * @return 是否更新成功
	 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	bool ReStartFromCurrentLocation();

	/**
	 * 更新目标位置
	 * @param NewTargetLocation 新的目标位置
	 * @return 是否更新成功
	 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	bool UpdateDestination(const FVector& NewLocation);
	
	/**
	 * 检查目标是否在攻击范围内
	 * @return 是否在攻击范围内
	 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	bool IsTargetInAttackRange() const;
	
	/**
	 * 获取追踪的位置
	 * @return 当前追踪的位置
	 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	FVector GetTargetLocation() const;
	
	/**
	 * 获取当前路径点索引
	 * @return 当前路径点索引
	 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	int32 GetCurrentPathIndex() const { return CurrentPathIndex; }
	
	/**
	 * 获取路径点数量
	 * @return 路径点数量
	 */
	UFUNCTION(BlueprintCallable, Category = "AI|Tasks")
	int32 GetPathPointsCount() const { return PathPoints.Num(); }

protected:
	/** 任务更新 */
	virtual void TickTask(float DeltaTime) override;

	// 移动控制
	void UpdateMovement(const FVector& CurrentLocation);
	bool HasReachedTarget(const FVector& CurrentLocation) const;
	
	// 事件检测
	void CheckEvents(const FVector& CurrentLocation, float DeltaTime);
	void CheckStuck(const FVector& CurrentLocation, float DeltaTime);
	void CheckTimeout(float DeltaTime);
	void CheckTargetMove();
	void CheckAttackRange(const FVector& CurrentLocation);
	
	// 事件处理
	void HandleStuck();
	void HandleTimeout();
	void HandleTargetMove();
	void HandleAttackRange();
	
	// 结束任务
	void FinishTask(bool bSuccess, ENavigationTaskResult Result);

private:
	void JumpTo(const FVector& TargetLocation);

public:
	/** 任务完成事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnFinished;

	/** 任务成功事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnSucceeded;

	/** 任务失败事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnFailed;

	/** 移动失败事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnMoveToFailed;
	
	/** 目标移动事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnTargetMoved;
	
	/** 路径被阻挡事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnPathBlocked;
	
	/** 卡住事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnStuck;
	
	/** 进入攻击范围事件 */
	UPROPERTY(BlueprintAssignable)
	FGenericGameplayTaskDelegate OnEnterAttackRange;

	/** 是否显示调试信息 */
	UPROPERTY(BlueprintReadWrite)
	bool ShowDebugInfo = false;

protected:
	/** 寻路对象 */
	UPROPERTY()
	TWeakObjectPtr<UAStarPathFindingBase> PathFindingMap;

	/** 路径点 */
	TArray<FVector> PathPoints;

	/** 当前路径点索引 */
	int32 CurrentPathIndex;

	/** 控制的角色 */
	TWeakObjectPtr<class ACharacterBase> ControlledCharacter;

	/** 移动组件 */
	TWeakObjectPtr<UCharacterMovementComponent> MovementComp;

	/** 判断是否到达目标点的阈值 */
	float ReachThreshold_X = 0.5;
	float ReachThreshold_Z = 3;
	
	/** 目标位置：导航任务的终点，计算导航路径时目标的初始位置 */
	FVector Destination;
	
	/** 上次位置 */
	FVector LastLocation;
	
	/** 卡住检测计时器 */
	float StuckCheckTimer;
	
	/** 导航超时计时器 */
	float TimeoutTimer;
	
	/** 导航请求参数 */
	FAINavigateRequest Request;
	
	/** 是否暂停 */
	bool bIsPaused;
}; 