// Fill out your copyright notice in the Description page of Project Settings.


#include "Common/XwFunctionLibrary.h"
#include "Character/XwPlayer.h"
#include "Character/XwEnemy.h"

float UXwFunctionLibrary::GetXpAttenuationOfLevel(const UObject* WorldContextObject, int PlayerLevl, int EnemyLevel)
{
	int LevelDelta = PlayerLevl - EnemyLevel;
	if (LevelDelta <= 2) {
		return 1.0;
	}
	else if (2 < LevelDelta && LevelDelta <= 5)	{
		return 1.0 - LevelDelta * 0.05;
	}
	else if (5 < LevelDelta && LevelDelta <= 10) {
		return 0.6 - LevelDelta * 0.02;
	}
	else {
		return 0.0;
	}
}

int UXwFunctionLibrary::GetXPCanObatain(const UObject* WorldContextObject, AActor* Player, AActor* Enemy)
{
	AXwPlayer* XwPlayer = Cast<AXwPlayer>(Player);
	AXwEnemy* XwEnemy = Cast<AXwEnemy>(Enemy);
	if (!XwPlayer || !XwEnemy) return 0;
	
	int OriginalXP = XwEnemy->ObtainXP;
	float Attenuation = UXwFunctionLibrary::GetXpAttenuationOfLevel(WorldContextObject,
	                                                                ICombatInterface::Execute_GetPlayerLevel(XwPlayer),
	                                                                ICombatInterface::Execute_GetPlayerLevel(XwEnemy));
	return OriginalXP * Attenuation;
}
