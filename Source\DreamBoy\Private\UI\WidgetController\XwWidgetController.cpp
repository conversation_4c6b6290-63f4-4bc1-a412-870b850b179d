// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/WidgetController/XwWidgetController.h"
#include "Player/XwPlayerController.h"
#include "Player/XwPlayerState.h"
#include "GAS/XwAbilitySystemComponent.h"
#include "GAS/GameAttribute/AttributeSetBase.h"

void UXwWidgetController::SetWidgetControllerParams(const FWidgetControllerParams& WCParams)
{
	PlayerController = WCParams.PC;
	PlayerState = WCParams.PS;
	AbilitySystemComponent = WCParams.ASC;
	AttributeSet = WCParams.AS;
}

AXwPlayerController* UXwWidgetController::GetXwPC()
{
	if (!XwPlayerController) {
		XwPlayerController = Cast<AXwPlayerController>(PlayerController);
	}
	return XwPlayerController;
}

AXwPlayerState* UXwWidgetController::GetXwPS()
{
	if (!XwPlayerState) {
		XwPlayerState = Cast<AXwPlayerState>(PlayerState);
	}
	return XwPlayerState;
}

UXwAbilitySystemComponent* UXwWidgetController::GetXwASC()
{
	if (!XwAbilitySystemComponent) {
		XwAbilitySystemComponent = Cast<UXwAbilitySystemComponent>(AbilitySystemComponent);
	}
	return XwAbilitySystemComponent;
}

UAttributeSetBase* UXwWidgetController::GetXwAS()
{
	if (!XwAttributeSet) {
		XwAttributeSet = Cast<UAttributeSetBase>(AttributeSet);
	}
	return XwAttributeSet;
}
