// Fill out your copyright notice in the Description page of Project Settings.

#include "Inventory/InventoryConfigDB.h"
#include "Engine/DataTable.h"
#include "Engine/World.h"

void UInventoryConfigDB::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	// 设置默认配置表路径（可以在项目设置中配置）
	if (!ConfigTableAsset.IsValid())
	{
		ConfigTableAsset = TSoftObjectPtr<UDataTable>(FSoftObjectPath(TEXT("/Game/Inventory/DT_InventoryDataBase")));
	}

	LoadConfigTable();
}

void UInventoryConfigDB::Deinitialize()
{
	ClearCache();
	Super::Deinitialize();
}

const FInventoryEntityConfig* UInventoryConfigDB::GetEntityConfig(int32 ConfigID) const
{
	if (const FInventoryEntityConfig* const* FoundConfig = ConfigCache.Find(ConfigID))
	{
		return *FoundConfig;
	}

	UE_LOG(LogTemp, Warning, TEXT("UInventoryConfigDB::GetEntityConfig - ConfigID %d not found"), ConfigID);
	return nullptr;
}

int32 UInventoryConfigDB::GetConfigIDByType(EInventoryType Type, int32 TypeID) const
{
	if (const TMap<int32, int32>* TypeMap = TypeToConfigMap.Find(Type))
	{
		if (const int32* FoundConfigID = TypeMap->Find(TypeID))
		{
			return *FoundConfigID;
		}
	}

	UE_LOG(LogTemp,
	       Warning,
	       TEXT("UInventoryConfigDB::GetConfigIDByType - Type %d, TypeID %d not found"),
	       (int32)Type,
	       TypeID);
	return 0;
}

bool UInventoryConfigDB::ValidateConfigID(int32 ConfigID) const
{
	return ConfigCache.Contains(ConfigID);
}

TArray<const FInventoryEntityConfig*> UInventoryConfigDB::GetAllConfigsByType(EInventoryType Type) const
{
	if (const TArray<const FInventoryEntityConfig*>* FoundConfigs = ConfigsByType.Find(Type))
	{
		return *FoundConfigs;
	}

	return TArray<const FInventoryEntityConfig*>();
}

int32 UInventoryConfigDB::GetConfigCountByType(EInventoryType Type) const
{
	if (const TArray<const FInventoryEntityConfig*>* FoundConfigs = ConfigsByType.Find(Type))
	{
		return FoundConfigs->Num();
	}

	return 0;
}

void UInventoryConfigDB::LoadConfigTable()
{
	ClearCache();

	// 构建正确的资产引用路径
	FString AssetPath = ConfigTableAsset.ToString();

	// 确保路径格式正确
	UE_LOG(LogTemp, Log, TEXT("尝试加载资产: %s"), *AssetPath);

	// 使用StaticLoadObject加载资产
	UDataTable* LoadedTable = Cast<UDataTable>(StaticLoadObject(UDataTable::StaticClass(), nullptr, *AssetPath));

	if (!LoadedTable)
	{
		UE_LOG(LogTemp, Error, TEXT("UInventoryConfigDB::LoadConfigTable - 无法加载DataTable: %s"), *AssetPath);

		// 尝试加载并检查对象类型
		UObject* LoadedObject = StaticLoadObject(UObject::StaticClass(), nullptr, *AssetPath);
		if (LoadedObject)
		{
			UE_LOG(LogTemp, Error, TEXT("加载的对象类型: %s"), *LoadedObject->GetClass()->GetName());
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("无法加载任何对象"));
		}

		return;
	}

	ConfigTable = LoadedTable;

	// 获取所有行数据
	TArray<FInventoryEntityConfig*> AllRows;
	ConfigTable->GetAllRows<FInventoryEntityConfig>(TEXT("UInventoryConfigDB::LoadConfigTable"), AllRows);

	// 构建缓存
	for (FInventoryEntityConfig* Row : AllRows)
	{
		if (Row && Row->ConfigID > 0)
		{
			// 添加到主缓存
			ConfigCache.Add(Row->ConfigID, Row);

			// 添加到类型分组缓存
			ConfigsByType.FindOrAdd(Row->Type).Add(Row);
		}
	}

	// 构建映射表
	BuildTypeToConfigMap();

	UE_LOG(LogTemp, Log, TEXT("UInventoryConfigDB::LoadConfigTable - Loaded %d configs"), ConfigCache.Num());
}

void UInventoryConfigDB::BuildTypeToConfigMap()
{
	TypeToConfigMap.Empty();

	for (const auto& [ConfigID, Config] : ConfigCache)
	{
		if (Config)
		{
			TypeToConfigMap.FindOrAdd(Config->Type).Add(Config->TypeID, ConfigID);
		}
	}

	UE_LOG(LogTemp,
	       Log,
	       TEXT("UInventoryConfigDB::BuildTypeToConfigMap - Built mapping for %d types"),
	       TypeToConfigMap.Num());
}

void UInventoryConfigDB::ClearCache()
{
	ConfigCache.Empty();
	TypeToConfigMap.Empty();
	ConfigsByType.Empty();
	ConfigTable = nullptr;
}

UInventoryConfigDB* UInventoryConfigDB::GetInventoryConfigDB(const UObject* WorldContext)
{
	if (const UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull))
	{
		if (UGameInstance* GameInstance = World->GetGameInstance())
		{
			return GameInstance->GetSubsystem<UInventoryConfigDB>();
		}
	}

	return nullptr;
}
