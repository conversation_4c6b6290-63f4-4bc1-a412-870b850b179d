// Fill out your copyright notice in the Description page of Project Settings.


#include "Data/LevelUpInfo.h"

int32 ULevelUpInfo::FindLevelForXP(int32 XP) const
{
	int32 Level = 1;
	bool bSearching = true;
	while (bSearching)
	{
		// LevelUpInformation[1] = Level 1 Info
		// LevelUpInformation[2] = Level 2 Info
		if (LevelUpInformation.Num() - 1 <= Level)
			return Level;

		if (XP >= LevelUpInformation[Level].LevelUpRequirement)
		{
			++Level;
		}
		else
		{
			bSearching = false;
		}
	}
	return Level;
}
