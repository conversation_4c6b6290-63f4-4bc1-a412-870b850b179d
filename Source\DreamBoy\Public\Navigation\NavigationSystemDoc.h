/**
 * 导航系统文档
 * 
 * 本文档描述了DreamBoy项目中的导航系统设计和使用方法。
 * 
 * 一、系统架构
 * -------------
 * 导航系统由以下几个主要部分组成：
 * 
 * 1. 核心数据结构
 *    - FGridTileMap: 基础导航网格，存储格子类型信息
 *    - FJumpPointMap: 跳点导航网格，用于跳点搜索算法
 * 
 * 2. 地图管理
 *    - AXwPaperTileMapActor: 扩展的PaperTileMapActor，支持导航功能
 *    - AGridTileMapManager: 管理所有TileMap和导航网格
 *    - UTileMapNavigationBuilder: 从PaperTileMap构建导航网格
 * 
 * 3. 寻路算法
 *    - UAStarPathFindingBase: 寻路算法基类
 *    - UJumpPointPathFinding: 跳点搜索算法实现
 * 
 * 4. 导航组件
 *    - UEnemyNavigationComponent: 敌人导航组件，管理敌人的导航功能
 *    - UDynamicPlatformComponent: 动态平台组件，用于将Actor注册为动态导航平台
 * 
 * 5. 导航任务
 *    - UAITask_NavigateTo: AI导航任务，控制AI角色沿着路径移动
 *    - UBTTask_FollowPath: 行为树任务，使用导航系统移动
 * 
 * 6. 工具类
 *    - UNavigationUtilities: 导航工具类，提供各种导航相关的实用函数
 * 
 * 
 * 二、动态导航系统
 * ---------------
 * 导航系统支持动态添加和移除导航障碍物或平台，主要通过以下方式实现：
 * 
 * 1. AXwPaperTileMapActor
 *    - 添加了对动态对象的支持，可以添加和移除动态对象
 *    - 动态对象的格子信息存储在TileMapActor中
 *    - 当获取TileMap数据时，会合并基础TileMap和动态对象的数据
 * 
 * 2. UDynamicPlatformComponent
 *    - 可以将任何Actor注册为动态导航平台
 *    - 自动计算Actor占据的格子
 *    - 支持不同类型的平台（地面、障碍物等）
 * 
 * 3. UNavigationUtilities
 *    - 提供了计算对象占据格子的工具函数
 *    - 支持不同类型的组件（PrimitiveComponent、PaperSpriteComponent等）
 * 
 * 
 * 三、意外事件处理
 * ---------------
 * 导航系统支持各种意外事件的处理，主要包括以下方面：
 * 
 * 1. 目标移动
 *    - 检测目标是否移动，超过容忍距离则触发事件
 *    - 支持重新寻路或结束任务
 * 
 * 2. 角色卡住
 *    - 检测角色在一段时间内移动距离是否太小
 *    - 支持重新寻路或结束任务
 * 
 * 3. 进入攻击范围
 *    - 检测角色是否进入攻击范围
 *    - 支持提前结束导航任务，切换到攻击行为
 * 
 * 4. 路径阻塞
 *    - 检测寻路失败或无法到达目标
 *    - 支持重试寻路或结束任务
 * 
 * 5. 导航超时
 *    - 设置导航任务的最大执行时间
 *    - 超时后自动结束任务
 * 
 * 每种事件都有对应的代理事件，可以绑定自定义处理函数：
 * - OnTargetMoved: 目标移动事件
 * - OnStuck: 卡住事件
 * - OnEnterAttackRange: 进入攻击范围事件
 * - OnPathBlocked: 路径阻塞事件
 * - OnFailed: 任务失败事件
 * - OnSucceeded: 任务成功事件
 * 
 * 
 * 四、使用方法
 * -----------
 * 
 * 1. 创建动态平台
 *    - 添加UDynamicPlatformComponent到Actor
 *    - 调用RegisterAsPlatform方法，指定平台类型
 *    - 平台类型可以是ENavigationTileType::Ground、ENavigationTileType::Obstacle等
 * 
 * 2. 移除动态平台
 *    - 调用UnregisterAsPlatform方法
 *    - 或者直接销毁Actor，EndPlay会自动注销
 * 
 * 3. 使用导航系统移动敌人
 *    - 敌人类需要包含UEnemyNavigationComponent
 *    - 使用UAITask_NavigateTo任务或UBTTask_FollowPath行为树节点
 *    - 配置意外事件处理参数
 * 
 * 
 * 五、示例代码
 * -----------
 * 
 * 1. 创建动态平台
 * ```
 * // 在蓝图中
 * UFUNCTION(BlueprintCallable)
 * void CreatePlatform()
 * {
 *     // 创建平台Actor
 *     APlatformActor* Platform = GetWorld()->SpawnActor<APlatformActor>(PlatformClass, Location, Rotation);
 *     
 *     // 获取或添加动态平台组件
 *     UDynamicPlatformComponent* PlatformComp = Platform->FindComponentByClass<UDynamicPlatformComponent>();
 *     if (!PlatformComp)
 *     {
 *         PlatformComp = NewObject<UDynamicPlatformComponent>(Platform);
 *         PlatformComp->RegisterComponent();
 *     }
 *     
 *     // 注册为地面类型的平台
 *     PlatformComp->RegisterAsPlatform(ENavigationTileType::Ground);
 * }
 * ```
 * 
 * 2. 使用导航系统移动敌人（带事件处理）
 * ```
 * // 在敌人AI控制器中
 * void AEnemyAIController::MoveToLocation(const FVector& TargetLocation)
 * {
 *     AXwEnemy* Enemy = Cast<AXwEnemy>(GetPawn());
 *     if (!Enemy)
 *     {
 *         return;
 *     }
 *     
 *     UEnemyNavigationComponent* NavComp = Enemy->GetNavigationComponent();
 *     if (!NavComp)
 *     {
 *         return;
 *     }
 *     
 *     UAStarPathFindingBase* PathFinder = NavComp->GetPathFinder();
 *     if (!PathFinder)
 *     {
 *         return;
 *     }
 *     
 *     // 创建导航请求
 *     FAINavigateRequest Request;
 *     Request.StartWolrdLocation = Enemy->GetFootLocation();
 *     Request.EndWorldLocation = TargetLocation;
 *     Request.Map = PathFinder;
 *     
 *     // 配置意外事件处理
 *     Request.bCheckTargetMove = true;
 *     Request.TargetMoveTolerance = 100.0f;
 *     Request.bCheckStuck = true;
 *     Request.StuckCheckTime = 1.0f;
 *     Request.StuckDistance = 10.0f;
 *     Request.bCheckAttackRange = true;
 *     Request.AttackRange = 150.0f;
 *     Request.TimeoutSeconds = 10.0f;
 *     
 *     // 创建导航任务
 *     UAITask_NavigateTo* NavTask = UAITask_NavigateTo::NavigateTo(this, Request);
 *     
 *     // 绑定事件处理函数
 *     NavTask->OnEnterAttackRange.AddDynamic(this, &AEnemyAIController::OnEnterAttackRange);
 *     NavTask->OnStuck.AddDynamic(this, &AEnemyAIController::OnStuck);
 *     NavTask->OnTargetMoved.AddDynamic(this, &AEnemyAIController::OnTargetMoved);
 *     
 *     // 激活任务
 *     NavTask->ReadyForActivation();
 * }
 * 
 * // 事件处理函数
 * void AEnemyAIController::OnEnterAttackRange()
 * {
 *     // 切换到攻击行为
 *     UBehaviorTreeComponent* BTComp = Cast<UBehaviorTreeComponent>(BrainComponent);
 *     if (BTComp)
 *     {
 *         BTComp->RequestExecution(AttackTask);
 *     }
 * }
 * 
 * void AEnemyAIController::OnStuck()
 * {
 *     // 尝试随机移动一段距离
 *     // ...
 * }
 * 
 * void AEnemyAIController::OnTargetMoved()
 * {
 *     // 更新目标位置
 *     // ...
 * }
 * ```
 */ 