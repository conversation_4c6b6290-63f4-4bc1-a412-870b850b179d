// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "InventoryTypes.generated.h"

// 前向声明
class UTexture2D;

/**
 * 背包实体类型枚举
 */
UENUM(BlueprintType)
enum class EInventoryType : uint8
{
    Weapon          UMETA(DisplayName = "Weapon"),
    Armor           UMETA(DisplayName = "Armor"),
    Accessory       UMETA(DisplayName = "Accessory"),
    Item            UMETA(DisplayName = "Item"),
    Skill           UMETA(DisplayName = "Skill"),
    Buff            UMETA(DisplayName = "Buff"),
    ImportantItem   UMETA(DisplayName = "ImportantItem")
};

/**
 * 实体稀有度枚举
 */
UENUM(BlueprintType)
enum class EEntityRarity : uint8
{
    Common      UMETA(DisplayName = "Common"),
    Uncommon    UMETA(DisplayName = "Uncommon"),
    Rare        UMETA(DisplayName = "Rare"),
    Epic        UMETA(DisplayName = "Epic"),
    Legendary   UMETA(DisplayName = "Legendary")
};

/**
 * 背包实体配置数据结构 (用于DataTable)
 */
USTRUCT(BlueprintType)
struct DREAMBOY_API FInventoryEntityConfig : public FTableRowBase
{
    GENERATED_BODY()

    /** 配置ID，唯一标识 */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Basic")
    int32 ConfigID = 0;

    /** 显示名称 */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Basic")
    FText DisplayName;

    /** 描述信息 */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Basic")
    FText Description;

    /** 图标资源 */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Basic")
    TSoftObjectPtr<UTexture2D> Icon;

    /** 实体类型 */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Basic")
    EInventoryType Type = EInventoryType::Item;

    /** 类型内ID，用于其他模块定位具体功能 */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Basic")
    int32 TypeID = 0;

    /** 稀有度 */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Basic")
    EEntityRarity Rarity = EEntityRarity::Common;

    FInventoryEntityConfig()
    {
        ConfigID = 0;
        DisplayName = FText::GetEmpty();
        Description = FText::GetEmpty();
        Icon = nullptr;
        Type = EInventoryType::Item;
        TypeID = 0;
        Rarity = EEntityRarity::Common;
    }
};

/**
 * 运行时背包实体
 */
USTRUCT(BlueprintType)
struct DREAMBOY_API FInventoryEntity
{
    GENERATED_BODY()

    /** 配置ID */
    UPROPERTY(BlueprintReadOnly, Category = "Basic")
    int32 ConfigID = 0;

    /** 实体类型 */
    UPROPERTY(BlueprintReadOnly, Category = "Basic")
    EInventoryType Type = EInventoryType::Item;

    /** 类型内ID */
    UPROPERTY(BlueprintReadOnly, Category = "Basic")
    int32 TypeID = 0;

    /** 是否为新获得的实体 */
    UPROPERTY(BlueprintReadWrite, Category = "Status")
    bool bIsNew = true;

    FInventoryEntity()
    {
        ConfigID = 0;
        Type = EInventoryType::Item;
        TypeID = 0;
        bIsNew = true;
    }

    FInventoryEntity(int32 InConfigID, EInventoryType InType, int32 InTypeID)
        : ConfigID(InConfigID)
        , Type(InType)
        , TypeID(InTypeID)
        , bIsNew(true)
    {
    }

    /** 检查是否为有效实体 */
    bool IsValid() const
    {
        return ConfigID > 0;
    }

    /** 重载相等操作符 */
    bool operator==(const FInventoryEntity& Other) const
    {
        return ConfigID == Other.ConfigID;
    }

    /** 重载不等操作符 */
    bool operator!=(const FInventoryEntity& Other) const
    {
        return !(*this == Other);
    }
};
