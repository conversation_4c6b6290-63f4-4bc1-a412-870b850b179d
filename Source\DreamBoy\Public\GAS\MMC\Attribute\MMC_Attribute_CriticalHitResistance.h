// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameplayModMagnitudeCalculation.h"
#include "MMC_Attribute_CriticalHitResistance.generated.h"

/**
 * 
 */
UCLASS()
class DREAMBOY_API UMMC_Attribute_CriticalHitResistance : public UGameplayModMagnitudeCalculation
{
	GENERATED_BODY()
public:
	UMMC_Attribute_CriticalHitResistance();

	virtual float CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const override;

private:
	FGameplayEffectAttributeCaptureDefinition ConstitutionDef;
};
