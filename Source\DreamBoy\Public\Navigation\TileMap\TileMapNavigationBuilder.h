#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Navigation/Core/XwNavTileMap.h"
#include "Navigation/Core/XwNavTypes.h"
#include "TileMapNavigationBuilder.generated.h"

class UPaperTileMapComponent;
class AXwPaperTileMapActor;
struct FJumpPointMap;

/**
 * TileMap导航数据构建器
 * 负责从PaperTileMap构建各种导航网格
 */
UCLASS()
class DREAMBOY_API UTileMapNavigationBuilder : public UObject
{
    GENERATED_BODY()
    
public:
    /**
     * 从PaperTileMapComponent构建基础导航网格
     * @param OutGrid 输出的导航网格
     * @param TileMapComponent 输入的TileMap组件
     * @return 是否构建成功
     */
    //UFUNCTION(BlueprintCallable, Category = "Navigation|Builder")
    static bool BuildBaseNavigationGrid(FGridTileMap& OutGrid, UPaperTileMapComponent* TileMapComponent);
    
    /**
     * 构建Jump Point导航网格
     * @param OutGrid 输出的Jump Point导航网格
     * @param TileMapComponent 输入的TileMap组件
     * @param MovementCaps 移动能力参数
     * @return 是否构建成功
     */
    //UFUNCTION(BlueprintCallable, Category = "Navigation|Builder")
    static bool BuildJumpPointGrid(FJumpPointMap& OutGrid, UPaperTileMapComponent* TileMapComponent, const FPathFindingAthleticAttr& MovementCaps);
    
    /**
     * 从基础导航网格构建Jump Point导航网格
     * @param OutGrid 输出的Jump Point导航网格
     * @param BaseGrid 输入的基础导航网格
     * @param MovementCaps 移动能力参数
     * @return 是否构建成功
     */
    //UFUNCTION(BlueprintCallable, Category = "Navigation|Builder")
    static bool BuildJumpPointGridFromBase(FJumpPointMap& OutGrid, const FGridTileMap& BaseGrid, const FPathFindingAthleticAttr& MovementCaps);
    
    /**
     * 从TileMapActor构建基础导航网格
     * @param OutGrid 输出的导航网格
     * @param TileMapActor 输入的TileMapActor
     * @return 是否构建成功
     */
    //UFUNCTION(BlueprintCallable, Category = "Navigation|Builder")
    static bool BuildNavigationGridFromActor(FGridTileMap& OutGrid, AXwPaperTileMapActor* TileMapActor);
    
private:
    // 从TileMap层提取导航数据
    static void ExtractNavigationData(TArray<TArray<ENavigationTileType>>& OutTiles, const UPaperTileMapComponent* TileMapComp);
    
    // 处理地形数据，标记地面等
    static void ProcessTerrainData(TArray<TArray<ENavigationTileType>>& InOutTiles);
}; 