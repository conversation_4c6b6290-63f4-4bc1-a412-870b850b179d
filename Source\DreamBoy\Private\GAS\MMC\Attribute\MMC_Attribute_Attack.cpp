// Fill out your copyright notice in the Description page of Project Settings.


#include "GAS/MMC/Attribute/MMC_Attribute_Attack.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "Interaction/CombatInterface.h"
#include "Character/CharacterBase.h"
#include "Data/XwCharacterConfigAsset.h"

UMMC_Attribute_Attack::UMMC_Attribute_Attack()
{
	StrengthDef.AttributeToCapture = UAttributeSetBase::GetStrengthAttribute();
	StrengthDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
	StrengthDef.bSnapshot = false;

	RelevantAttributesToCapture.Add(StrengthDef);
}

float UMMC_Attribute_Attack::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	// Gather tags from source and target
	const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
	const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluationParameters;
	EvaluationParameters.SourceTags = SourceTags;
	EvaluationParameters.TargetTags = TargetTags;

	float Strength = 0.f;
	GetCapturedAttributeMagnitude(StrengthDef, Spec, EvaluationParameters, Strength);
	Strength = FMath::Max<float>(Strength, 0.f);

	//int32 PlayerLevel = 1;
	//if (Spec.GetContext().GetSourceObject()->Implements<UCombatInterface>())
	//{
	//	PlayerLevel = ICombatInterface::Execute_GetPlayerLevel(Spec.GetContext().GetSourceObject());
	//}
	float BasicValue = 0.0;
	if (ACharacterBase* Character = Cast<ACharacterBase>(Spec.GetContext().GetSourceObject()))
	{
		if (UXwCharacterConfigAsset* ConfigAsset = Character->GetConfigAsset())
		{
			BasicValue = ConfigAsset->BasicValue_Attack;
		}
	}
	

	return BasicValue + 1.f * Strength;
}
