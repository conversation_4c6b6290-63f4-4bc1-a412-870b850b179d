#include "Navigation/Tasks/AITask_NavigateTo.h"
#include "AIController.h"
#include "Character/CharacterBase.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Navigation/Core/XwNavPathFinder.h"
#include "Navigation/Algorithms/JumpPointSearch/JumpPointTileMap.h"
#include "Navigation/Components/EnemyNavigationComponent.h"
#include "DrawDebugHelpers.h"

UAITask_NavigateTo* UAITask_NavigateTo::NavigateTo(AAIController* Controller, const FAINavigateRequest& InRequest)
{
	if (!Controller) return nullptr;

	UAITask_NavigateTo* NewTask = NewAITask<UAITask_NavigateTo>(*Controller);
	NewTask->SetUp(Controller, InRequest);

	return NewTask;
}

void UAITask_NavigateTo::SetUp(AAIController* Controller, const FAINavigateRequest& InRequest)
{
	bTickingTask = true;
	bIsPaused = false;
	// 保存请求参数
	Request = InRequest;
	Destination = InRequest.EndWorldLocation;
	ShowDebugInfo = InRequest.ShowDebugInfo;

	OwnerController = Controller;
	if (Request.NavComponent) {
		PathFindingMap = Request.NavComponent->GetPathFinder();
	}
}

void UAITask_NavigateTo::Activate()
{
	Super::Activate();

	ControlledCharacter = Cast<ACharacterBase>(OwnerController->GetPawn());
	if (ControlledCharacter.IsValid())
	{
		MovementComp = ControlledCharacter->GetCharacterMovement();
		LastLocation = ControlledCharacter->GetFootLocation();
	}

	if (!MovementComp.IsValid())
	{
		FinishTask(false, ENavigationTaskResult::Failed);
		return;
	}

	if (!ReStartFromCurrentLocation())
	{
		// 没有有效路径
		FinishTask(false, ENavigationTaskResult::InvalidPath);
		return;
	}
}

void UAITask_NavigateTo::PauseTask()
{
	bIsPaused = true;
	if (MovementComp.IsValid())
	{
		MovementComp->StopMovementImmediately();
	}
}

void UAITask_NavigateTo::ResumeTask()
{
	bIsPaused = false;
}

bool UAITask_NavigateTo::ReStartFromCurrentLocation()
{
	TArray<FVector> NewPath = PathFindingMap->FindPath(ControlledCharacter->GetFootLocation(), GetTargetLocation());
	// 检查新路径是否有效
	if (NewPath.Num() <= 1)
	{
		return false; // 找不到新路径
	}

	// 更新路径
	PathPoints = NewPath;
	Destination = PathPoints.Last();// 将终点更新为与Grid对齐的坐标
	CurrentPathIndex = 0;

	// 重置状态
	LastLocation = ControlledCharacter->GetFootLocation();
	StuckCheckTimer = 0.0;
	TimeoutTimer = 0.0;

	return true;
}

bool UAITask_NavigateTo::UpdateDestination(const FVector& NewLocation)
{
	if (!PathFindingMap.IsValid() || !ControlledCharacter.IsValid())
	{
		return false;
	}
	
	// 检查目标是否真的移动了
	if (FVector::DistSquared(Destination, NewLocation) < FMath::Square(Request.TargetMoveTolerance))
	{
		return true; // 目标没有明显移动，保持当前路径
	}
	
	// 更新目标位置
	Destination = NewLocation;	
	return ReStartFromCurrentLocation();
}

bool UAITask_NavigateTo::IsTargetInAttackRange() const
{
	if (!ControlledCharacter.IsValid() || Request.AttackRange <= 0.0f)
	{
		return false;
	}
	
	const FVector CurrentLocation = ControlledCharacter->GetFootLocation();
	float DistanceToTarget = FVector::Distance(CurrentLocation, GetTargetLocation());
	
	return DistanceToTarget <= Request.AttackRange;
}

FVector UAITask_NavigateTo::GetTargetLocation() const
{
	if (Request.FollowTarget != nullptr) {
		if (auto* Character = Cast<ACharacterBase>(Request.FollowTarget)) {
			return Character->GetFootLocation();
		}
		else {
			return Request.FollowTarget->GetActorLocation();
		}
	}
	else {
		return Destination;
	}	
}

void UAITask_NavigateTo::TickTask(float DeltaTime)
{
	Super::TickTask(DeltaTime);

	if (bIsPaused) return;

	if (!MovementComp.IsValid() || !ControlledCharacter.IsValid() || !PathFindingMap.IsValid() ||
		CurrentPathIndex >= PathPoints.Num())
	{
		FinishTask(false, ENavigationTaskResult::Failed);
		return;
	}

	
	const FVector CurrentLocation = ControlledCharacter->GetFootLocation();
	FVector TargetPoint = PathPoints[CurrentPathIndex];
	
	// 检测各种事件
	CheckEvents(CurrentLocation, DeltaTime);
	
	// 到达检测（刚生成的时候，CurrentPathIndex为0且就在角色脚下，所以直接判定为已到达。如果改进一下寻路判定可以删掉这段……）
	if (CurrentPathIndex == 0 || HasReachedTarget(CurrentLocation))
	{
		CurrentPathIndex++;
		if (CurrentPathIndex >= PathPoints.Num())
		{
			// 到达终点
			FinishTask(true, ENavigationTaskResult::Success);
			return;
		}

		TargetPoint = PathPoints[CurrentPathIndex];
		// 在关键节点根据预计算好的行为，判断是否要跳跃
		const FJumpPointMap* JumpPointMap = static_cast<const FJumpPointMap*>(PathFindingMap->GetTileMapData());
		FIntVector2 LocalPos_Current = JumpPointMap->WorldToLocal(CurrentLocation);
		FIntVector2 LocalPos_Target = JumpPointMap->WorldToLocal(TargetPoint);
		const FNavPointInfo* CurrentNavPointInfo = JumpPointMap->GetNavPoint(LocalPos_Current);
		if (CurrentNavPointInfo) {
			ENavLinkType MovementAction = ENavLinkType::MoveLink;
			if (const auto* LinkInfo = CurrentNavPointInfo->GetLinkTo(LocalPos_Target)) {
				switch (LinkInfo->Type)
				{
				case JumpLink:
					MovementAction = ENavLinkType::JumpLink;
					break;
				default:
					break;
				}
			}
			if (MovementAction == ENavLinkType::JumpLink) {
				JumpTo(TargetPoint);
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("UAITask_NavigateTo::TickTask: Current has no corresponding NavPoint"));
		}
	}
	
	// Debug draw
	if (ShowDebugInfo)
	{
		DrawDebugSphere(GetWorld(), CurrentLocation, 4, 8, FColor::Blue, false, DeltaTime);
		FVector PreLocation, CurLocation;
		PreLocation = CurLocation = CurrentLocation;
		for (int Index = CurrentPathIndex; Index < PathPoints.Num(); ++Index)
		{
			CurLocation = PathPoints[Index];
			DrawDebugSphere(GetWorld(), CurLocation, 2, 4, FColor::Red, false, DeltaTime);
			PreLocation = CurLocation;
		}
	}

	// 更新移动逻辑
	UpdateMovement(CurrentLocation);
}

void UAITask_NavigateTo::UpdateMovement(const FVector& CurrentLocation)
{
	const FVector CurrentTarget = PathPoints[CurrentPathIndex];
	const FVector DeltaToTarget = CurrentTarget - CurrentLocation;
	const FVector Direction = DeltaToTarget.GetSafeNormal2D();

	float DirectionX = FMath::Sign(Direction.X);
	if (DirectionX != 0)
	{
		ControlledCharacter->SetActorRotation(FRotator(0, DirectionX > 0 ? 0.f : 180.f, 0));
	}
	// 水平移动输入
	if (FMath::Abs(DeltaToTarget.X) < 0.2) {
		MovementComp->Velocity.X = 0.0;
	}
	else {
		MovementComp->Velocity.X = MovementComp->MaxWalkSpeed * DirectionX;
	}
}

bool UAITask_NavigateTo::HasReachedTarget(const FVector& CurrentLocation) const
{
	FVector Distance = CurrentLocation - PathPoints[CurrentPathIndex];
	bool Result = FMath::Abs(Distance.X) < ReachThreshold_X &&
		FMath::Abs(Distance.Z) < ReachThreshold_Z &&
		!MovementComp->IsFalling();
	return Result;
}

void UAITask_NavigateTo::CheckEvents(const FVector& CurrentLocation, float DeltaTime)
{
	// 检测是否卡住
	if (Request.bCheckStuck)
	{
		CheckStuck(CurrentLocation, DeltaTime);
	}
	// 检测导航是否超时
	if (Request.bCheckTimeout && Request.TimeoutSeconds > 0.0f)
	{
		CheckTimeout(DeltaTime);
	}
	// 检测目标是否移动
	if (Request.bCheckTargetMove)
	{
		CheckTargetMove();
	}
	// 检测是否进入攻击范围
	if (Request.bCheckAttackRange)
	{
		CheckAttackRange(CurrentLocation);
	}
}

void UAITask_NavigateTo::CheckStuck(const FVector& CurrentLocation, float DeltaTime)
{
	// 累加检测时间
	StuckCheckTimer += DeltaTime;
	
	// 达到检测时间
	if (StuckCheckTimer >= Request.StuckCheckTime)
	{
		// 计算在检测时间内移动的距离
		float MovedDistance = FVector::Distance(LastLocation, CurrentLocation);
		
		//// 添加调试输出
		//UE_LOG(LogTemp, Warning, TEXT("UAITask_NavigateTo::CheckStuck: LastLocation=%s, CurrentLocation=%s, MovedDistance=%f"),
		//	*LastLocation.ToString(),
		//	*CurrentLocation.ToString(),
		//	MovedDistance);

		// 如果移动距离小于卡住检测距离，则认为卡住
		if (MovedDistance < Request.StuckDistance)
		{
			if (!MovementComp->IsFalling()) {
				HandleStuck();
			}
			else {
				// 先不清除状态，等待落地再执行HandleStuck();
				return;
			}
		}
		
		// 重置检测
		StuckCheckTimer = 0.0f;
		LastLocation = CurrentLocation;
	}
}

void UAITask_NavigateTo::CheckTimeout(float DeltaTime)
{
	// 累加超时时间
	TimeoutTimer += DeltaTime;
	
	// 检查是否超时
	if (TimeoutTimer >= Request.TimeoutSeconds)
	{
		HandleTimeout();
	}
}

void UAITask_NavigateTo::CheckTargetMove()
{
	// 只在跟踪目标的模式时才有意义
	if(Request.FollowTarget == nullptr) return;

	// 检查目标与路径终点的距离
	float DistanceToPathEnd = FVector::DistSquared(GetTargetLocation(), PathPoints.Last());
	
	// 如果目标移动超过容忍距离，则需要重新寻路
	if (DistanceToPathEnd > FMath::Square(Request.TargetMoveTolerance))
	{
		HandleTargetMove();
	}
}

void UAITask_NavigateTo::CheckAttackRange(const FVector& CurrentLocation)
{
	// 检查是否进入攻击范围
	if (IsTargetInAttackRange())
	{
		HandleAttackRange();
	}
}

void UAITask_NavigateTo::HandleStuck()
{
	if (ShowDebugInfo)
	{
		UE_LOG(LogTemp, Warning, TEXT("AITask_NavigateTo: Character is stuck!"));
	}
	
	// 触发卡住事件
	OnStuck.Broadcast();
	
	if (Request.bRepathWhenStuck)
	{
		// 如果重新寻路失败，则任务失败
		if (!ReStartFromCurrentLocation())
		{
			FinishTask(false, ENavigationTaskResult::Stuck);
		}
	}
	else
	{
		FinishTask(false, ENavigationTaskResult::Stuck);
	}
}

void UAITask_NavigateTo::HandleTimeout()
{
	if (ShowDebugInfo)
	{
		UE_LOG(LogTemp, Warning, TEXT("AITask_NavigateTo: Navigation timeout!"));
	}

	if (Request.bRepathWhenTimeout)
	{
		// 如果重新寻路失败，则任务失败
		if (!ReStartFromCurrentLocation())
		{
			FinishTask(false, ENavigationTaskResult::InvalidPath);
		}
	}
	else
	{
		FinishTask(false, ENavigationTaskResult::Timeout);
	}
}

void UAITask_NavigateTo::HandleTargetMove()
{
	if (ShowDebugInfo)
	{
		UE_LOG(LogTemp, Warning, TEXT("AITask_NavigateTo: Target moved!"));
	}
	
	// 触发目标移动事件
	OnTargetMoved.Broadcast();
	
	if (Request.bRepathWhenTargetMoves)
	{
		// 如果重新寻路失败，则任务失败
		if (!ReStartFromCurrentLocation())
		{
			FinishTask(false, ENavigationTaskResult::InvalidPath);
		}
	}
}

void UAITask_NavigateTo::HandleAttackRange()
{
	if (ShowDebugInfo)
	{
		UE_LOG(LogTemp, Warning, TEXT("AITask_NavigateTo: Target in attack range!"));
	}
	
	// 触发进入攻击范围事件
	OnEnterAttackRange.Broadcast();
	
	if (Request.bFinishInAttackRange)
	{
		// 结束任务
		FinishTask(true, ENavigationTaskResult::InAttackRange);
	}
}

void UAITask_NavigateTo::FinishTask(bool bSuccess, ENavigationTaskResult Result)
{
	// 停止移动
	if (MovementComp.IsValid())
	{
		MovementComp->StopMovementImmediately();
	}
	
	// 广播导航完成事件
	OnNavigationCompleted.Broadcast(bSuccess, Result);
	
	// 根据结果广播相应事件
	if (bSuccess)
	{
		switch (Result)
		{
		case ENavigationTaskResult::Success:
			OnSucceeded.Broadcast();
			break;
		case ENavigationTaskResult::InAttackRange:
			OnEnterAttackRange.Broadcast();
			break;
		default:
			break;
		}
	}
	else
	{
		switch (Result)
		{
		case ENavigationTaskResult::Failed:
			OnFailed.Broadcast();
			break;
		case ENavigationTaskResult::InvalidPath:
			OnPathBlocked.Broadcast();
			break;
		case ENavigationTaskResult::Stuck:
			OnStuck.Broadcast();
			break;
		case ENavigationTaskResult::Timeout:
			OnFailed.Broadcast();
			break;
		case ENavigationTaskResult::TargetMoved:
			OnTargetMoved.Broadcast();
			break;
		default:
			OnFailed.Broadcast();
			break;
		}
	}
	
	// 结束任务
	EndTask();
}

void UAITask_NavigateTo::JumpTo(const FVector& EndLocation)
{
	const FVector CurrentLocation = ControlledCharacter->GetFootLocation();
	const float ZDiff = EndLocation.Z - CurrentLocation.Z;
	const float XDiff = FMath::Abs(EndLocation.X - CurrentLocation.X);
	float Gravity = abs(MovementComp->GetGravityZ()); // 重力加速度（默认 980）
	float JumpZVelocity = MovementComp->JumpZVelocity; // 垂直初速度
	float MaxWalkSpeed = MovementComp->MaxWalkSpeed; // 水平最大速度

	float TotalTimeX = XDiff / MaxWalkSpeed;
	// 由于NavMap中有预计算，这里可以只考虑跳跃上升过程中达到ZDiff的情况
	float TotalTimeZ = (JumpZVelocity - FMath::Sqrt(JumpZVelocity * JumpZVelocity - 2 * Gravity * ZDiff)) / Gravity;
	float TotalTime = FMath::Max(TotalTimeX, TotalTimeZ);

	float Vz = (ZDiff + 0.5 * Gravity * FMath::Square(TotalTime)) / TotalTime;
	ControlledCharacter->LaunchCharacter(FVector(0.0, 0.0, Vz), false, true);
}

//bool UAITask_NavigateTo::CanJumpToLocation(FVector TargetLocation)
//{
//	/*const FVector CurrentLocation = ControlledCharacter->GetFootLocation();
//	const float ZDiff = TargetLocation.Z - CurrentLocation.Z;
//	const float XDiff = TargetLocation.X - CurrentLocation.X;
//	float Gravity = abs(MovementComp->GetGravityZ()); // 重力加速度（默认 980）
//	float JumpZVelocity = MovementComp->JumpZVelocity; // 垂直初速度
//	float MaxWalkSpeed = MovementComp->MaxWalkSpeed; // 水平最大速度
//	// 计算总跳跃时间（从起跳到落地）
//	float TotalTime =(2 * JumpZVelocity) / Gravity; // 上升 + 下落时间
//	if (ZDiff > 0.0)
//	{
//		// 先满足跳跃距离，然后再满足跳跃高度
//		float MaxHorizontalDistance = MaxWalkSpeed * TotalTime;
//		if (FMath::Abs(XDiff) > MaxHorizontalDistance){
//			return false;
//		}
//
//		if (FMath::Abs(XDiff) <= MaxHorizontalDistance / 2.0) {
//			return ZDiff <= FMath::Square(JumpZVelocity) / (2 * Gravity);
//		}
//
//		float Vz = JumpZVelocity, Vx = MaxWalkSpeed;
//		//      ---
//		//     /   \
//		//    /     \  <- 计算跳跃轨迹上此处的Z值
//		//   /       |
//		//  /--XDiff-|
//		// Current  Target
//		float MaxDeltaHeight = (Vz / Vx) * FMath::Abs(XDiff) - (Gravity / 2.0) * FMath::Square(FMath::Abs(XDiff) / Vx);
//		return ZDiff <= MaxDeltaHeight;
//	}
//	else
//	{
//		return FMath::Abs(XDiff) <= MaxWalkSpeed * TotalTime;
//	}*/
//	return false;
//}
