// Fill out your copyright notice in the Description page of Project Settings.


#include "Player/XwPlayerState.h"
#include "GAS/XwAbilitySystemComponent.h"
#include "GAS/GameAttribute/AttributeSetBase.h"

AXwPlayerState::AXwPlayerState()
{
	bReplicates = false;

	AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>("AbilitySystemComponent");
	AbilitySystemComponent->SetIsReplicated(false);

	AttributeSet = CreateDefaultSubobject<UAttributeSetBase>("AttributeSet");
}

UAbilitySystemComponent* AXwPlayerState::GetAbilitySystemComponent() const
{
	return AbilitySystemComponent;
}

int AXwPlayerState::GetRequiredXpForLevelUp(int Level)
{
	return Level * 100 + 200;
}

int AXwPlayerState::GetRequiredXpOfLevel(int Level)
{
	int RetXP = 0;
	for (int i = 1; i < Level; ++i)
	{
		RetXP += GetRequiredXpForLevelUp(i);
	}
	return RetXP;
}

int AXwPlayerState::GetLevelOfAbsoluteXP(int XP)
{
	int XP_Left = XP;
	int RetLevel = 0;
	while (XP_Left >= 0)
	{
		++RetLevel;
		XP_Left -= GetRequiredXpForLevelUp(RetLevel);
	}
	return RetLevel;
}

void AXwPlayerState::AddToXP(int32 InXP)
{
	XP += InXP;
	OnXPChangedDelegate.Broadcast(XP);
}

void AXwPlayerState::SetXP(int32 InXP)
{
	XP = InXP;
	OnXPChangedDelegate.Broadcast(XP);
}

void AXwPlayerState::AddToLevel(int32 InLevel)
{
	Level += InLevel;
	OnLevelChangedDelegate.Broadcast(Level, true);
}

void AXwPlayerState::SetLevel(int32 InLevel)
{
	Level = InLevel;
	OnLevelChangedDelegate.Broadcast(Level, false);
}
