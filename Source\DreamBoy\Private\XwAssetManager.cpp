// Fill out your copyright notice in the Description page of Project Settings.


#include "XwAssetManager.h"
#include "XwGameplayTags.h"


UXwAssetManager& UXwAssetManager::Get()
{
	check(GEngine);

	UXwAssetManager* XwAssetManager = Cast<UXwAssetManager>(GEngine->AssetManager);
	return *XwAssetManager;
}

void UXwAssetManager::StartInitialLoading()
{
	Super::StartInitialLoading();

	FXwGameplayTags::InitializeNativeGameplayTags();
}