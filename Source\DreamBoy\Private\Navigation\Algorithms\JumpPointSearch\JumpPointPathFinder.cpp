#include "Navigation/Algorithms/JumpPointSearch/JumpPointPathFinder.h"
#include "Common/PriorityQueue.h"
#include "DrawDebugHelpers.h"

void UJumpPointPathFinding::Initialize(const FJumpPointMap& InMap, const FPathFindingAthleticAttr& Attr)
{
	PathFindingMap = InMap;
	PathFindingMap.InitWithAthleticAttr(Attr);

	OnInitFinished();
}


void UJumpPointPathFinding::InitializeWithMap(const FJumpPointMap& InMap)
{
	// assume InMap is Initialized
	PathFindingMap = InMap;
}

TArray<FVector> UJumpPointPathFinding::FindPath(const FVector& StartWorldPos, const FVector& EndWorldPos)
{
	TArray<FVector> WorldPath;

	// 转换坐标到节点空间
	FIntVector2 StartLoc = PathFindingMap.WorldToLocal(StartWorldPos);
	FIntVector2 GoaltLoc = PathFindingMap.WorldToLocal(EndWorldPos);
	// 边界检查
	if (!PathFindingMap.IsValidPos(StartLoc.X, StartLoc.Y) ||
		!PathFindingMap.IsValidPos(GoaltLoc.X, GoaltLoc.Y))
	{
		return WorldPath;
	}
	if (!PathFindingMap.FindNearestGround(StartLoc, StartLoc)) return WorldPath;
	if (!PathFindingMap.FindNearestGround(GoaltLoc, GoaltLoc)) return WorldPath;

	const FNavPointInfo* pStart = PathFindingMap.GetNavPoint(StartLoc);
	const FNavPointInfo* pGoal = PathFindingMap.GetNavPoint(GoaltLoc);
	if (!pStart || !pGoal) return WorldPath;

	const FNavPointInfo StartNode = *pStart;
	const FNavPointInfo GoalNode = *pGoal;

	// A* 算法数据结构
	TPriorityQueue<FNavPointInfo> OpenQueue;
	TSet<FNavPointInfo> ClosedSet;
	TMap<FNavPointInfo, FNavPointInfo> CameFrom;
	TMap<FNavPointInfo, float> CostSoFar;

	// 初始化起点
	OpenQueue.Enqueue(StartNode, 0.0f);
	CostSoFar.Add(StartNode, 0.0f);

	while (!OpenQueue.IsEmpty())
	{
		// 获取当前最低代价节点
		FNavPointInfo CurrentNode;
		float CurrentPriority;
		OpenQueue.Dequeue(CurrentNode, CurrentPriority);

		// 到达目标点
		if (CurrentNode == GoalNode)
		{
			// 重建路径
			TArray<FNavPointInfo> NodePath;
			FNavPointInfo Current = GoalNode;
			while (Current != StartNode)
			{
				NodePath.Add(Current);
				Current = CameFrom[Current];
			}
			NodePath.Add(StartNode);
			Algo::Reverse(NodePath);

			// 转换为世界坐标
			for (const FNavPointInfo& Node : NodePath)
			{
				WorldPath.Add(PathFindingMap.LocalToWorld(Node.Position) + FVector(0.0, 0.0, float(-PathFindingMap.GridTileSize) / 2.0));
			}
			return WorldPath;
		}

		ClosedSet.Add(CurrentNode);

		// 遍历所有邻居
		for (auto NeighborPoint : GetNeighbors(CurrentNode.Position))
		{
			const FNavPointInfo Neighbor = *PathFindingMap.GetNavPoint(NeighborPoint);

			// 跳过已处理的节点
			if (ClosedSet.Contains(Neighbor)) continue;

			//// 检查跳跃可行性
			//if (!CanJumpBetweenNodes(CurrentNode, Neighbor, JumpCap)) continue;

			// 计算新代价
			// 累加G Cost ：基础移动代价和垂直跳跃惩罚（高度差越大，代价越高）
			const float NewCost = CostSoFar[CurrentNode] + Distance(CurrentNode.Position, Neighbor.Position);

			// 更新代价和路径
			if (!CostSoFar.Contains(Neighbor) || NewCost < CostSoFar[Neighbor])
			{
				CostSoFar.Add(Neighbor, NewCost);
				CameFrom.Add(Neighbor, CurrentNode);

				// 计算启发式代价（曼哈顿距离 + 高度差）
				const float Heuristic = Distance(Neighbor.Position, GoalNode.Position);

				const float Priority = NewCost + Heuristic;
				OpenQueue.Enqueue(Neighbor, Priority);
			}
		}
	}

	return WorldPath;
}

TArray<FIntVector2> UJumpPointPathFinding::GetNeighbors(FIntVector2 Pos) const
{
	TSet<FIntVector2> Result;

	const auto* Point = PathFindingMap.GetNavPoint(Pos);
	if (!Point) return Result.Array();

	for (const auto& LinkInfo : Point->KeyPointLinks)
	{
		Result.Add(LinkInfo.To);
	}

	// 水平搜索两边能走过去的节点
	const FNavPointInfo* CurNavPoint = nullptr;
	FIntVector2 Neighbor;

	CurNavPoint = Point;
	Neighbor = Pos;
	Neighbor.X += 1;
	while (auto* RightNavPoint = PathFindingMap.GetNavPoint(Neighbor))
	{
		if (!CurNavPoint->CanWalkToHorizontalRightPoint) break;

		Result.Add(RightNavPoint->Position);
		CurNavPoint = RightNavPoint;
		Neighbor.X += 1;
	}
	CurNavPoint = Point;
	Neighbor = Pos;
	Neighbor.X -= 1;
	while (auto* LeftNavPoint = PathFindingMap.GetNavPoint(Neighbor))
	{
		if (!CurNavPoint->CanWalkToHorizontalLeftPoint) break;

		Result.Add(LeftNavPoint->Position);
		CurNavPoint = LeftNavPoint;
		Neighbor.X -= 1;
	}

	return Result.Array();
}

int32 UJumpPointPathFinding::Distance(FIntVector2 From, FIntVector2 To) const
{
	return FMath::Abs(To.X - From.X) + FMath::Abs(To.Y - From.Y);
}

void UJumpPointPathFinding::DrawDebug() const
{
	//FlushPersistentDebugLines(GetWorld());
	Debug_DrawKeyPoints();
}

void UJumpPointPathFinding::Debug_DrawKeyPoints() const
{
	for (const auto& Pair : PathFindingMap.CachedNavPoints)
	{
		auto NavPoint = Pair.Value;
		FVector Center = PathFindingMap.LocalToWorld(NavPoint.Position);
		int DrawTimes = 0;
		float BaseRadius = PathFindingMap.GridTileSize / 2.0 / 2.0;
		if (NavPoint.IsLeftWall) {
			float Radius = BaseRadius * (1.0 - 0.15 * DrawTimes);
			DrawDebugSphere(GetWorld(), Center, Radius, 16, FColor::Black, true);
			Debug_DrawConnections(NavPoint);
			++DrawTimes;
		}
		if (NavPoint.IsRightWall) {
			float Radius = BaseRadius * (1.0 - 0.15 * DrawTimes);
			DrawDebugSphere(GetWorld(), Center, Radius, 16, FColor::White, true);
			Debug_DrawConnections(NavPoint);
			++DrawTimes;
		}
		if (NavPoint.IsLeftEdge) {
			float Radius = BaseRadius * (1.0 - 0.15 * DrawTimes);
			DrawDebugSphere(GetWorld(), Center, Radius, 16, FColor::Red, true);
			Debug_DrawConnections(NavPoint);
			++DrawTimes;
		}
		if (NavPoint.IsRightEdge) {
			float Radius = BaseRadius * (1.0 - 0.15 * DrawTimes);
			DrawDebugSphere(GetWorld(), Center, Radius, 16, FColor::Green, true);
			Debug_DrawConnections(NavPoint);
			++DrawTimes;
		}
		if (NavPoint.IsFallTile) {
			float Radius = BaseRadius * (0.35);
			DrawDebugSphere(GetWorld(), Center, Radius, 16, FColor::Blue, true);
			Debug_DrawConnections(NavPoint);
			//++DrawTimes;
		}
	}
}

void UJumpPointPathFinding::Debug_DrawConnections(const FNavPointInfo& Point) const
{
	for (const auto& LinkInfo : Point.KeyPointLinks)
	{
		const FIntVector2 Pos = LinkInfo.To;
		FVector Start = PathFindingMap.LocalToWorld(Point.Position);
		FVector End = PathFindingMap.LocalToWorld(Pos);
		FColor LineColor = FColor::Yellow;
		if (auto* EndPoint = PathFindingMap.GetNavPoint(Pos)) {
			if (auto* LinkInfo2 = EndPoint->GetLinkTo(Point)) {
				LineColor = FColor::Green;
			}
		}
		DrawDebugLine(GetWorld(), Start, End, LineColor, true);
	}
}

void UJumpPointPathFinding::Debug_DrawGrid(FIntVector2 Pos, FColor Color /*= FColor::Blue*/, bool bPersistentLines /*= false*/, float LifeTime /*= -1.f*/) const
{
	FVector Center = PathFindingMap.LocalToWorld(Pos);
	DrawDebugBox(GetWorld(), Center,
		FVector(PathFindingMap.GridTileSize / 2.0, 1, PathFindingMap.GridTileSize / 2.0),
		Color, bPersistentLines, LifeTime);
}
