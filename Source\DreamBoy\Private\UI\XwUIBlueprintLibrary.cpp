// Fill out your copyright notice in the Description page of Project Settings.

#include "UI/XwUIBlueprintLibrary.h"
#include "UI/UserWidget/XwUserWidget.h"
#include "UI/HUD/XwHUD.h"
#include "GAS/XwAbilitySystemLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"

// 静态变量定义
TWeakObjectPtr<UXwUserWidget> UXwUIBlueprintLibrary::CurrentBuildWidget = nullptr;

UXwUserWidget* UXwUIBlueprintLibrary::ShowCharacterBuildWidget(const UObject* WorldContextObject,
                                                               TSubclassOf<UXwUserWidget> BuildWidgetClass)
{
	if (!BuildWidgetClass)
	{
		UE_LOG(LogTemp, Warning, TEXT("BuildWidgetClass is null"));
		return nullptr;
	}

	// 如果已经有Build界面在显示，先隐藏它
	if (CurrentBuildWidget.IsValid())
	{
		HideCharacterBuildWidget(WorldContextObject);
	}

	// 获取PlayerController
	APlayerController* PC = UGameplayStatics::GetPlayerController(WorldContextObject, 0);
	if (!PC)
	{
		UE_LOG(LogTemp, Warning, TEXT("PlayerController not found"));
		return nullptr;
	}

	// 创建Widget
	UXwUserWidget* BuildWidget = CreateWidget<UXwUserWidget>(PC, BuildWidgetClass);
	if (!BuildWidget)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to create BuildWidget"));
		return nullptr;
	}

	// 获取并设置控制器
	UCharacterBuildWidgetController* BuildController =
		UXwAbilitySystemLibrary::GetCharacterBuildWidgetController(WorldContextObject);
	if (BuildController)
	{
		BuildWidget->SetWidgetController(BuildController);
	}

	// 添加到视口
	BuildWidget->AddToViewport(100); // 高Z-Order确保在最前面

	// 设置输入模式
	SetBuildWidgetInputMode(WorldContextObject, true);

	// 存储引用
	CurrentBuildWidget = BuildWidget;

	return BuildWidget;
}

void UXwUIBlueprintLibrary::HideCharacterBuildWidget(const UObject* WorldContextObject)
{
	if (CurrentBuildWidget.IsValid())
	{
		CurrentBuildWidget->RemoveFromParent();
		CurrentBuildWidget = nullptr;

		// 恢复游戏输入模式
		RestoreGameInputMode(WorldContextObject);
	}
}

UXwUserWidget* UXwUIBlueprintLibrary::GetCharacterBuildWidget(const UObject* WorldContextObject)
{
	return CurrentBuildWidget.IsValid() ? CurrentBuildWidget.Get() : nullptr;
}

bool UXwUIBlueprintLibrary::IsCharacterBuildWidgetVisible(const UObject* WorldContextObject)
{
	return CurrentBuildWidget.IsValid() && CurrentBuildWidget->IsInViewport();
}

void UXwUIBlueprintLibrary::ToggleCharacterBuildWidget(const UObject* WorldContextObject,
														TSubclassOf<UXwUserWidget> BuildWidgetClass)
{
	if (IsCharacterBuildWidgetVisible(WorldContextObject))
	{
		HideCharacterBuildWidget(WorldContextObject);
	}
	else
	{
		ShowCharacterBuildWidget(WorldContextObject, BuildWidgetClass);
	}
}

void UXwUIBlueprintLibrary::SetBuildWidgetInputMode(const UObject* WorldContextObject, bool bUIOnly)
{
	APlayerController* PC = UGameplayStatics::GetPlayerController(WorldContextObject, 0);
	if (!PC)
		return;

	if (bUIOnly)
	{
		// 设置为UI输入模式
		FInputModeUIOnly InputMode;
		if (CurrentBuildWidget.IsValid())
		{
			InputMode.SetWidgetToFocus(CurrentBuildWidget->TakeWidget());
		}
		InputMode.SetLockMouseToViewportBehavior(EMouseLockMode::DoNotLock);
		PC->SetInputMode(InputMode);
		PC->SetShowMouseCursor(true);
	}
	else
	{
		// 设置为游戏和UI混合模式
		FInputModeGameAndUI InputMode;
		if (CurrentBuildWidget.IsValid())
		{
			InputMode.SetWidgetToFocus(CurrentBuildWidget->TakeWidget());
		}
		InputMode.SetLockMouseToViewportBehavior(EMouseLockMode::DoNotLock);
		InputMode.SetHideCursorDuringCapture(false);
		PC->SetInputMode(InputMode);
		PC->SetShowMouseCursor(true);
	}
}

void UXwUIBlueprintLibrary::RestoreGameInputMode(const UObject* WorldContextObject)
{
	APlayerController* PC = UGameplayStatics::GetPlayerController(WorldContextObject, 0);
	if (!PC)
		return;

	// 恢复为游戏输入模式
	FInputModeGameOnly InputMode;
	PC->SetInputMode(InputMode);
	PC->SetShowMouseCursor(false);
}

TArray<FBuildItemInfo> UXwUIBlueprintLibrary::CreateMockBuildItems()
{
	TArray<FBuildItemInfo> MockItems;

	// 创建一些测试用的物品数据（使用新的简化结构）

	// 武器
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Weapon, 1001)); // 懒惰之枕
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Weapon, 1002)); // 欲望之鞭
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Weapon, 1003)); // 愤怒之锤

	// 防具
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Armor, 2001)); // 懒惰之被
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Armor, 2002)); // 欲望之纱
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Armor, 2003)); // 愤怒之甲

	// 饰品
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Accessory, 3001)); // 懒惰之眼罩
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Accessory, 3002)); // 欲望之项链

	// 技能
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Skill1, 4001)); // 懒惰落脚点
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Skill2, 4002)); // 懒惰锚点
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Skill1, 4003)); // 暗送秋波
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Skill2, 4004)); // 魅惑之舞

	// 道具
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Item, 5001)); // 懒惰安眠药
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Item, 5002)); // 欲望香水

	// Buff
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Buff, 6001)); // 懒惰迟缓
	MockItems.Add(FBuildItemInfo(EBuildSlotType::Buff, 6002)); // 欲望魅惑

	return MockItems;
}

FText UXwUIBlueprintLibrary::GetSlotTypeDisplayName(EBuildSlotType SlotType)
{
	switch (SlotType)
	{
	case EBuildSlotType::Weapon:
		return FText::FromString(TEXT("武器"));
	case EBuildSlotType::Armor:
		return FText::FromString(TEXT("防具"));
	case EBuildSlotType::Accessory:
		return FText::FromString(TEXT("饰品"));
	case EBuildSlotType::Skill1:
		return FText::FromString(TEXT("技能1"));
	case EBuildSlotType::Skill2:
		return FText::FromString(TEXT("技能2"));
	case EBuildSlotType::Item:
		return FText::FromString(TEXT("道具"));
	case EBuildSlotType::Buff:
		return FText::FromString(TEXT("增益"));
	default:
		return FText::FromString(TEXT("未知"));
	}
}

FText UXwUIBlueprintLibrary::GetSlotTypeKeyHint(EBuildSlotType SlotType)
{
	switch (SlotType)
	{
	case EBuildSlotType::Skill1:
		return FText::FromString(TEXT("L"));
	case EBuildSlotType::Skill2:
		return FText::FromString(TEXT("R"));
	case EBuildSlotType::Item:
		return FText::FromString(TEXT("X"));
	default:
		return FText::GetEmpty();
	}
}

bool UXwUIBlueprintLibrary::CanEquipItemToSlot(const FBuildItemInfo& ItemInfo, EBuildSlotType SlotType)
{
	return ItemInfo.SlotType == SlotType;
}
