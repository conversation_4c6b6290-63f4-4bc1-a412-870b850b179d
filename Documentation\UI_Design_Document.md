# 角色Build配置界面 - UMG设计方案

## 1. 整体架构

### MVC架构集成
- **Model**: `UCharacterBuildWidgetController` + GAS属性系统
- **View**: `WBP_CharacterBuild` (UMG Blueprint Widget)
- **Controller**: `UCharacterBuildWidgetController`

### 继承关系
```
UUserWidget (UE基类)
└── UXwUserWidget (项目基类)
    └── WBP_CharacterBuild (蓝图Widget)
```

## 2. UMG布局设计

### 2.1 根容器结构
```
Canvas Panel (Root)
├── Background Image (半透明背景 + 暗角效果)
├── Top Status Bar (顶部状态栏)
├── Main Content Area (主要内容区域)
└── Bottom Detail Panel (底部详情面板)
```

### 2.2 顶部状态栏 (Top Status Bar)
**容器**: Horizontal Box
```
HorizontalBox
├── Player Level Section
│   ├── Text Block: "Lv."
│   ├── Text Block: {PlayerLevel} (绑定到控制器)
│   └── Circular Progress Bar: XP进度环
├── Spacer (弹性空间)
├── Health Bar Section
│   ├── Progress Bar: 血量条 (红色)
│   └── Text Block: "{CurrentHP}/{MaxHP}"
├── Mana Bar Section
│   ├── Progress Bar: 法力条 (蓝色)
│   └── Text Block: "{CurrentMP}/{MaxMP}"
└── Energy Section
    └── Horizontal Box: 4个菱形Energy指示器
        ├── Image: 菱形1 (激活/未激活状态)
        ├── Image: 菱形2
        ├── Image: 菱形3
        └── Image: 菱形4
```

### 2.3 主要内容区域 (Main Content Area)
**容器**: Horizontal Box
```
HorizontalBox
├── Left Panel: Item List (可见性绑定)
│   └── Scroll Box
│       └── Vertical Box: 动态生成物品列表
│           └── WBP_BuildItemEntry (自定义Widget)
├── Center Panel: Build Slots + Character Model
│   ├── Character Avatar (角色头像/模型显示区域)
│   └── Build Slots Container
│       ├── Skill1 Slot (左上) + Key Hint "L"
│       ├── Skill2 Slot (右上) + Key Hint "R"  
│       ├── Weapon Slot (中央，较大)
│       ├── Armor Slot (左侧)
│       ├── Accessory Slot (右侧)
│       ├── Item Slot (左下) + Key Hint "X"
│       └── Buff Slot (右下)
└── Right Panel: Attributes Display
    └── Vertical Box
        ├── Text Block: "Attributes"
        ├── Attribute Entry: "Atk: {Value}"
        ├── Attribute Entry: "Def: {Value}"
        ├── Attribute Entry: "Crt: {Value}"
        ├── Attribute Entry: "Spd: {Value}"
        └── ... (其他属性)
```

### 2.4 底部详情面板 (Bottom Detail Panel)
**容器**: Horizontal Box
```
HorizontalBox
├── Item Icon
│   └── Image: 选中物品图标
├── Item Info
│   ├── Text Block: 物品名称
│   └── Rich Text Block: 物品描述 (支持富文本格式)
└── Spacer
```

## 3. 自定义Widget组件

### 3.1 WBP_BuildSlot (Build槽位组件)
**用途**: 可重用的装备槽位组件
**结构**:
```
Button (可点击区域)
└── Overlay
    ├── Background Circle (背景圆形)
    ├── Item Icon (装备图标)
    ├── Focus Border (选中边框效果)
    └── Key Hint Text (快捷键提示，如"L", "R", "X")
```

**蓝图属性**:
- `SlotType`: EBuildSlotType (槽位类型)
- `IsSelected`: bool (是否选中)
- `KeyHint`: FText (快捷键提示文本)
- `ItemInfo`: FBuildItemInfo (当前装备的物品信息)

### 3.2 WBP_BuildItemEntry (物品列表条目)
**用途**: 物品列表中的单个条目
**结构**:
```
Button (可点击区域)
└── Horizontal Box
    ├── Item Icon
    ├── Vertical Box
    │   ├── Item Name
    │   └── Item Brief Description
    └── Equipped Indicator (装备标识)
```

### 3.3 WBP_AttributeEntry (属性显示条目)
**用途**: 属性面板中的单个属性显示
**结构**:
```
Horizontal Box
├── Attribute Name (如"Atk:")
├── Spacer
└── Attribute Value
```

## 4. 数据绑定设计

### 4.1 控制器绑定
在`WBP_CharacterBuild`的蓝图中：
```cpp
// Event: Widget Controller Set
void OnWidgetControllerSet()
{
    if (UCharacterBuildWidgetController* BuildController = 
        Cast<UCharacterBuildWidgetController>(WidgetController))
    {
        // 绑定委托
        BuildController->OnSlotSelected.AddDynamic(this, &ThisClass::OnSlotSelected);
        BuildController->OnItemSelected.AddDynamic(this, &ThisClass::OnItemSelected);
        BuildController->OnItemEquipped.AddDynamic(this, &ThisClass::OnItemEquipped);
        BuildController->OnAvailableItemsUpdated.AddDynamic(this, &ThisClass::OnAvailableItemsUpdated);
        BuildController->OnBuildConfigUpdated.AddDynamic(this, &ThisClass::OnBuildConfigUpdated);
        
        // 广播初始值
        BuildController->BroadcastInitialValue();
    }
}
```

### 4.2 状态栏数据绑定
```cpp
// 绑定玩家等级
Text_PlayerLevel->SetText(FText::AsNumber(PlayerLevel));

// 绑定血量条
ProgressBar_Health->SetPercent(CurrentHealth / MaxHealth);
Text_Health->SetText(FText::Format(LOCTEXT("HealthFormat", "{0}/{1}"), 
    FText::AsNumber(CurrentHealth), FText::AsNumber(MaxHealth)));

// 绑定Energy菱形
for (int32 i = 0; i < 4; ++i)
{
    EnergyDiamonds[i]->SetColorAndOpacity(i < CurrentEnergy ? 
        FLinearColor::White : FLinearColor(0.3f, 0.3f, 0.3f, 1.0f));
}
```

## 5. 交互逻辑实现

### 5.1 槽位选择逻辑
```cpp
// WBP_BuildSlot 的点击事件
UFUNCTION(BlueprintCallable)
void OnSlotClicked()
{
    if (UCharacterBuildWidgetController* Controller = GetBuildController())
    {
        Controller->SelectSlot(SlotType);
    }
}

// WBP_CharacterBuild 的槽位选择响应
UFUNCTION()
void OnSlotSelected(EBuildSlotType SelectedSlotType)
{
    // 更新槽位选中状态
    UpdateSlotSelection(SelectedSlotType);
    
    // 显示物品列表
    ShowItemList(true);
    
    // 播放UI动画
    PlaySlotSelectionAnimation();
}
```

### 5.2 物品列表管理
```cpp
UFUNCTION()
void OnAvailableItemsUpdated(const TArray<FBuildItemInfo>& AvailableItems)
{
    // 清空现有列表
    ItemListContainer->ClearChildren();
    
    // 动态创建物品条目
    for (const FBuildItemInfo& Item : AvailableItems)
    {
        UWBPBuildItemEntry* ItemEntry = CreateWidget<UWBPBuildItemEntry>(this, ItemEntryClass);
        ItemEntry->SetItemInfo(Item);
        ItemEntry->OnItemClicked.AddDynamic(this, &ThisClass::OnItemEntryClicked);
        ItemListContainer->AddChild(ItemEntry);
    }
    
    // 自动滚动到当前装备的物品
    ScrollToCurrentEquippedItem();
}
```

### 5.3 Focus管理
```cpp
// Focus状态管理
void UpdateFocusState()
{
    if (bIsItemListVisible)
    {
        // Focus到物品列表
        if (ItemListContainer->GetChildrenCount() > 0)
        {
            ItemListContainer->GetChildAt(CurrentItemIndex)->SetFocus();
        }
    }
    else
    {
        // Focus到当前选中的槽位
        GetSlotWidget(CurrentSelectedSlot)->SetFocus();
    }
}
```

## 6. 动画和视觉效果

### 6.1 UI动画
- **槽位选择动画**: 选中时的缩放和发光效果
- **列表显示动画**: 从右侧滑入的动画
- **物品选择动画**: 高亮和边框动画
- **属性更新动画**: 数值变化时的闪烁效果

### 6.2 视觉状态
- **槽位状态**: 空槽位、已装备、选中、Focus四种状态
- **物品状态**: 普通、选中、已装备三种状态
- **列表状态**: 显示、隐藏两种状态

## 7. 性能优化考虑

### 7.1 对象池
- 物品列表条目使用对象池避免频繁创建销毁
- 属性显示条目复用

### 7.2 数据缓存
- 物品数据缓存避免重复查询
- 属性计算结果缓存

### 7.3 渲染优化
- 不可见时停止更新
- 使用Invalidation Box优化重绘

## 8. 实现步骤

1. **创建基础控制器** ✅ (已完成)
2. **创建UMG蓝图Widget**
3. **实现基础布局**
4. **创建自定义子Widget**
5. **实现数据绑定**
6. **添加交互逻辑**
7. **实现动画效果**
8. **性能优化和测试**
