// Fill out your copyright notice in the Description page of Project Settings.

#include "Navigation/TileMap/GridTileMapManager.h"
#include "Navigation/TileMap/XwPaperTileMapActor.h"
#include "PaperTileMapComponent.h"
// #include "Kismet/GameplayStatics.h"
#include "Character/CharacterBase.h"
#include "Navigation/Core/XwNavTypes.h"
#include "Navigation/Core/XwNavTileMap.h"
#include "Navigation/Algorithms/JumpPointSearch/JumpPointTileMap.h"
#include "Navigation/TileMap/TileMapNavigationBuilder.h"
#include "Navigation/Components/EnemyNavigationComponent.h"
#include "Character/XwEnemy.h"
#include "EngineUtils.h"

void AGridTileMapManager::BeginPlay()
{
	Super::BeginPlay();
	Initialze();
}

void AGridTileMapManager::BeginDestroy()
{
	DeInitialze();
	Super::BeginDestroy();
}

void AGridTileMapManager::Initialze()
{
	if (InstanceInCurrentWorld == nullptr)
	{
		InstanceInCurrentWorld = this;
	}
}

void AGridTileMapManager::DeInitialze()
{
	if (InstanceInCurrentWorld == this)
	{
		InstanceInCurrentWorld = nullptr;
	}
}

AXwPaperTileMapActor *AGridTileMapManager::FindTileMapByLocation(FVector WorldLocation)
{
	for (auto &Iter : TileMapRegistry)
	{
		AXwPaperTileMapActor *MapActor = Iter.Key;
		if (!MapActor)
			continue;
		if (MapActor->GetRenderComponent()->Bounds.GetBox().IsInside(WorldLocation))
		{
			return MapActor;
		}
	}
	return nullptr;
}

bool AGridTileMapManager::RegisterTileMap(AXwPaperTileMapActor *TileMapActor)
{
	if (!TileMapActor)
	{
		return false;
	}

	TileMapRegistry.Add(TileMapActor);
	return true;
}

void AGridTileMapManager::UnRegisterTileMap(AXwPaperTileMapActor *TileMapActor)
{
	TileMapRegistry.Remove(TileMapActor);
}

bool AGridTileMapManager::RegisterTileMap(AActor *Character, EEnemyRace EnemyTypeID)
{
	if (!Character)
	{
		return false;
	}

	AXwPaperTileMapActor *MapBelongsTo = FindTileMapByLocation(Character->GetActorLocation());
	if (!MapBelongsTo)
	{
		return false;
	}

	auto &Maps = TileMapRegistry.FindOrAdd(MapBelongsTo);
	if (Maps.Find(EnemyTypeID) == nullptr)
	{
		Maps.Add(EnemyTypeID, GetDefaultMap(Character, EnemyTypeID));
	}
	return true;
}

void AGridTileMapManager::UnRegisterTileMap(AActor *Character, EEnemyRace EnemyTypeID)
{
	/*if (!Character)
	{
		return;
	}

	AXwPaperTileMapActor* MapBelongsTo = FindTileMapByLocation(Character->GetActorLocation());
	if(!MapBelongsTo)
	{
		return;
	}

	auto* Maps = TileMapRegistry.Find(MapBelongsTo);
	if (Maps)
	{
		Maps->Remove(EnemyTypeID);
	}*/
}

TSharedPtr<FGridTileMap> AGridTileMapManager::GetTileMap(AXwPaperTileMapActor *FindInThisMap, EEnemyRace EnemyTypeID)
{
	if (!FindInThisMap)
	{
		return nullptr;
	}

	auto *Maps = TileMapRegistry.Find(FindInThisMap);
	if (!Maps)
	{
		return nullptr;
	}
	else if (!Maps->Contains(EnemyTypeID))
	{
		return nullptr;
	}
	else
	{
		return *(Maps->Find(EnemyTypeID));
	}
}

TSharedPtr<FJumpPointMap> AGridTileMapManager::GetJumpPointMap(AXwPaperTileMapActor *FindInThisMap, EEnemyRace EnemyTypeID)
{
	TSharedPtr<FGridTileMap> BaseMap = GetTileMap(FindInThisMap, EnemyTypeID);
	if (!BaseMap.IsValid())
	{
		return nullptr;
	}

	return StaticCastSharedPtr<FJumpPointMap>(BaseMap);
}

AGridTileMapManager *AGridTileMapManager::GetManagerInCurrentWorld()
{
	return InstanceInCurrentWorld;
}

void AGridTileMapManager::NotifyTileMapChanged(AXwPaperTileMapActor *TileMapActor)
{
	if (!TileMapActor)
	{
		return;
	}

	// 重新生成所有依赖此TileMap的导航数据
	RebuildNavigationForTileMap(TileMapActor);
}

void AGridTileMapManager::RebuildNavigationForTileMap(AXwPaperTileMapActor *TileMapActor)
{
	if (!TileMapActor)
	{
		return;
	}

	// 查找依赖此TileMap的所有导航数据
	auto *Maps = TileMapRegistry.Find(TileMapActor);
	if (!Maps)
	{
		return;
	}

	// 重新生成所有导航数据
	for (auto &Pair : *Maps)
	{
		EEnemyRace EnemyTypeID = Pair.Key;

		// 查找所有使用此类型导航数据的敌人
		TArray<AActor *> Enemies;
		for (TActorIterator<AXwEnemy> It(GetWorld()); It; ++It)
		{
			AXwEnemy *Enemy = *It;
			if (Enemy && Enemy->Race == EnemyTypeID)
			{
				Enemies.Add(Enemy);
			}
		}

		// 重新生成导航数据
		TSharedPtr<FGridTileMap> NewMap = nullptr;
		if (Enemies.Num() > 0)
		{
			NewMap = GetDefaultMap(Enemies[0], EnemyTypeID);
		}

		// 更新导航数据
		if (NewMap.IsValid())
		{
			Pair.Value = NewMap;

			// 通知所有敌人更新导航组件
			for (AActor *Enemy : Enemies)
			{
				AXwEnemy *XwEnemy = Cast<AXwEnemy>(Enemy);
				if (XwEnemy)
				{
					UEnemyNavigationComponent *NavComp = XwEnemy->GetNavigationComponent();
					if (NavComp)
					{
						NavComp->InitializeNavigation(EnemyTypeID);
					}
				}
			}
		}
	}
}

TSharedPtr<FGridTileMap> AGridTileMapManager::GetDefaultMap(AActor *Character, EEnemyRace EnemyTypeID)
{
	// 这里需要根据EnemyTypeID来选择不同的地图
	// switch (EnemyTypeID)
	//{
	// default: {

	//}
	//}

	AXwPaperTileMapActor *MapBelongsTo = FindTileMapByLocation(Character->GetActorLocation());
	if (!MapBelongsTo)
	{
		return nullptr;
	}

	ACharacterBase *CharacterBase = Cast<ACharacterBase>(Character);
	if (!CharacterBase)
	{
		return nullptr;
	}

	TSharedPtr<FGridTileMap> BaseMap = MapBelongsTo->GetTileMapData();
	if (!BaseMap.IsValid())
	{
		return nullptr;
	}

	return CreateJumpPointMap(CharacterBase, BaseMap);
}

TSharedPtr<FJumpPointMap> AGridTileMapManager::CreateJumpPointMap(ACharacterBase *Character, const TSharedPtr<FGridTileMap> &BaseMap)
{
	if (!Character || !BaseMap.IsValid())
	{
		return nullptr;
	}

	TSharedPtr<FJumpPointMap> JumpPointMap = MakeShared<FJumpPointMap>();
	FPathFindingAthleticAttr Attr = FPathFindingAthleticAttr::GenerateFromCharacter(Character, BaseMap->GridTileSize);

	JumpPointMap->InitWithParameter(*BaseMap, Attr);
	return JumpPointMap;
}

AGridTileMapManager *AGridTileMapManager::InstanceInCurrentWorld = nullptr;
