// Fill out your copyright notice in the Description page of Project Settings.


#include "Navigation/TileMap/XwPaperTileMapActor.h"
#include "Navigation/TileMap/GridTileMapManager.h"
#include "Navigation/Core/XwNavTileMap.h"
#include "Navigation/TileMap/TileMapNavigationBuilder.h"

void AXwPaperTileMapActor::BeginPlay()
{
	Super::BeginPlay();
	
	// 使用TileMapNavigationBuilder创建导航网格
	GridTileMap = MakeShared<FGridTileMap>();
	CachedMergedTileMap = nullptr;
	bNeedRebuildMergedMap = true;
	
	UTileMapNavigationBuilder::BuildNavigationGridFromActor(*GridTileMap, this);

	// 注册到TileMapManager
	if (AGridTileMapManager* Manager = AGridTileMapManager::GetManagerInCurrentWorld()) {
		Manager->RegisterTileMap(this);
	}
}

void AXwPaperTileMapActor::BeginDestroy()
{
	if (AGridTileMapManager* Manager = AGridTileMapManager::GetManagerInCurrentWorld()) {
		Manager->UnRegisterTileMap(this);
	}

	Super::BeginDestroy();
}

bool AXwPaperTileMapActor::AddDynamicObject(AActor* Actor, const TArray<FIntPoint>& OccupiedTiles, ENavigationTileType TileType)
{
	if (!Actor || OccupiedTiles.Num() == 0 || !GridTileMap.IsValid())
	{
		return false;
	}
	
	// 检查Actor是否已经存在
	if (DynamicObjects.Contains(Actor))
	{
		// 如果已存在，先移除旧的
		RemoveDynamicObject(Actor);
	}
	
	// 创建新的动态对象
	FDynamicTileObject DynamicObject(Actor);
	
	// 添加占据的格子
	for (const FIntPoint& Tile : OccupiedTiles)
	{
		if (GridTileMap->IsValidPos(Tile.X, Tile.Y))
		{
			DynamicObject.OccupiedTiles.Add(Tile);
			DynamicObject.TileTypes.Add(Tile, TileType);
			
			// 更新格子到对象的映射
			TileToObjectMap.Add(Tile, Actor);
		}
	}
	
	// 如果没有有效的格子，返回失败
	if (DynamicObject.OccupiedTiles.Num() == 0)
	{
		return false;
	}
	
	// 添加到动态对象列表
	DynamicObjects.Add(Actor, DynamicObject);
	
	// 标记需要重新构建合并地图
	bNeedRebuildMergedMap = true;
	
	// 通知GridTileMapManager更新导航数据
	if (AGridTileMapManager* Manager = AGridTileMapManager::GetManagerInCurrentWorld())
	{
		Manager->NotifyTileMapChanged(this);
	}
	
	return true;
}

bool AXwPaperTileMapActor::RemoveDynamicObject(AActor* Actor)
{
	if (!Actor)
	{
		return false;
	}
	
	// 检查Actor是否存在
	FDynamicTileObject* DynamicObject = DynamicObjects.Find(Actor);
	if (!DynamicObject)
	{
		return false;
	}
	
	// 移除格子到对象的映射
	for (const FIntPoint& Tile : DynamicObject->OccupiedTiles)
	{
		TileToObjectMap.Remove(Tile);
	}
	
	// 从动态对象列表中移除
	DynamicObjects.Remove(Actor);
	
	// 标记需要重新构建合并地图
	bNeedRebuildMergedMap = true;
	
	// 通知GridTileMapManager更新导航数据
	if (AGridTileMapManager* Manager = AGridTileMapManager::GetManagerInCurrentWorld())
	{
		Manager->NotifyTileMapChanged(this);
	}
	
	return true;
}

TSharedPtr<FGridTileMap> AXwPaperTileMapActor::GetTileMapData()
{
	// 如果没有动态对象，直接返回基础TileMap
	if (DynamicObjects.Num() == 0)
	{
		return GridTileMap;
	}
	
	// 如果需要重新构建合并地图，则进行合并
	if (bNeedRebuildMergedMap || !CachedMergedTileMap.IsValid())
	{
		CachedMergedTileMap = MergeTileMaps();
		bNeedRebuildMergedMap = false;
	}
	
	return CachedMergedTileMap;
}

void AXwPaperTileMapActor::ClearDynamicObjects()
{
	// 清空动态对象列表和格子到对象的映射
	DynamicObjects.Empty();
	TileToObjectMap.Empty();
	
	// 标记需要重新构建合并地图
	bNeedRebuildMergedMap = true;
	
	// 通知GridTileMapManager更新导航数据
	if (AGridTileMapManager* Manager = AGridTileMapManager::GetManagerInCurrentWorld())
	{
		Manager->NotifyTileMapChanged(this);
	}
}

bool AXwPaperTileMapActor::IsTileOccupiedByDynamicObject(const FIntPoint& Position) const
{
	return TileToObjectMap.Contains(Position);
}

AActor* AXwPaperTileMapActor::GetDynamicObjectAtTile(const FIntPoint& Position) const
{
	TWeakObjectPtr<AActor> ActorPtr = TileToObjectMap.FindRef(Position);
	return ActorPtr.IsValid() ? ActorPtr.Get() : nullptr;
}

TSharedPtr<FGridTileMap> AXwPaperTileMapActor::MergeTileMaps() const
{
	if (!GridTileMap.IsValid())
	{
		return nullptr;
	}
	
	// 创建新的合并TileMap
	TSharedPtr<FGridTileMap> MergedMap = MakeShared<FGridTileMap>();	
	*MergedMap = *GridTileMap;
	
	// 合并动态对象的数据
	for (const auto& Pair : DynamicObjects)
	{
		const FDynamicTileObject& DynamicObject = Pair.Value;
		
		// 检查Actor是否有效
		if (!DynamicObject.OwnerActor.IsValid())
		{
			continue;
		}
		
		// 更新格子类型
		for (const auto& TilePair : DynamicObject.TileTypes)
		{
			const FIntPoint& Tile = TilePair.Key;
			ENavigationTileType TileType = TilePair.Value;
			
			if (MergedMap->IsValidPos(Tile.X, Tile.Y))
			{
				MergedMap->TileMap[Tile.X][Tile.Y] = static_cast<ENavigationTileType>(TileType);
			}
		}
	}
	
	// 重新处理地形数据，标记地面等
	MergedMap->InitTiles();
	MergedMap->RefreshCache();
	
	return MergedMap;
}

void AXwPaperTileMapActor::UpdateMergedTileMap()
{
	// 标记需要重新构建合并地图
	bNeedRebuildMergedMap = true;
	
	// 通知GridTileMapManager更新导航数据
	if (AGridTileMapManager* Manager = AGridTileMapManager::GetManagerInCurrentWorld())
	{
		Manager->NotifyTileMapChanged(this);
	}
}
