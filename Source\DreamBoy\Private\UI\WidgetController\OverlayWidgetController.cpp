// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/WidgetController/OverlayWidgetController.h"
#include "Player/XwPlayerController.h"
#include "Player/XwPlayerState.h"
#include "GAS/XwAbilitySystemComponent.h"
#include "GAS/GameAttribute/AttributeSetBase.h"

void UOverlayWidgetController::BroadcastInitialValue()
{
	UAttributeSetBase* AS = CastChecked<UAttributeSetBase>(AttributeSet);

	OnHealthChanged.Broadcast(AS->GetHealth());
	OnMaxHealthChanged.Broadcast(AS->GetMaxHealth());

	OnManaChanged.Broadcast(AS->GetMana());
	OnMaxManaChanged.Broadcast(AS->GetMaxMana());

	OnEnergyChanged.Broadcast(AS->GetEnergy());
	OnMaxEnergyChanged.Broadcast(AS->GetMaxEnergy());
}

void UOverlayWidgetController::BindCallbacksToDependencies()
{
	GetXwPS()->OnXPChangedDelegate.AddUObject(this, &UOverlayWidgetController::OnXPChanged);
	GetXwPS()->OnLevelChangedDelegate.AddLambda([this](int32 NewLevel, bool bLevelUp)
	                                              { OnPlayerLevelChangedDelegate.Broadcast(NewLevel, bLevelUp); });

	UAttributeSetBase* AS = GetXwAS();

	AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetHealthAttribute()).
		AddLambda([this](const FOnAttributeChangeData& Data) { OnHealthChanged.Broadcast(Data.NewValue); });

	AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetMaxHealthAttribute()).
		AddLambda([this](const FOnAttributeChangeData& Data) { OnMaxHealthChanged.Broadcast(Data.NewValue); });

	AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetManaAttribute()).
		AddLambda([this](const FOnAttributeChangeData& Data) { OnManaChanged.Broadcast(Data.NewValue); });

	AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetMaxManaAttribute()).
		AddLambda([this](const FOnAttributeChangeData& Data) { OnMaxManaChanged.Broadcast(Data.NewValue); });

	AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetEnergyAttribute()).
		AddLambda([this](const FOnAttributeChangeData& Data) { OnEnergyChanged.Broadcast(Data.NewValue); });

	AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetMaxEnergyAttribute()).
		AddLambda([this](const FOnAttributeChangeData& Data) { OnMaxEnergyChanged.Broadcast(Data.NewValue); });
}

void UOverlayWidgetController::OnXPChanged(int32 NewXP)
{
	const int32 Level = AXwPlayerState::GetLevelOfAbsoluteXP(NewXP);
	const int32 MaxLevel = AXwPlayerState::GetMaxLevel();

	if (Level <= MaxLevel && Level > 0)
	{
		const float XPBarPercent = float(NewXP - AXwPlayerState::GetRequiredXpOfLevel(Level)) /
		                           float(AXwPlayerState::GetRequiredXpForLevelUp(Level));
		OnXPPercentChangedDelegate.Broadcast(XPBarPercent);
	}
}
