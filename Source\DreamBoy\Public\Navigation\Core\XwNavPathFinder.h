#pragma once

#include "CoreMinimal.h"
#include "Navigation/Core/XwNavTypes.h"
#include "Navigation/Core/XwNavTileMap.h"
#include "XwNavPathFinder.generated.h"

class UPaperTileMapComponent;

class PathFindingUtils
{
public:
	// 从 Tilemap 生成所有可站立节点
	static void BuildPathFindingMap(FGridTileMap& PathFindingMap, const UPaperTileMapComponent* TileMapComp);
};


/**
 * 寻路算法基类
 * 提供基础的寻路接口和通用功能
 */
UCLASS()
class DREAMBOY_API UAStarPathFindingBase : public UObject
{
	GENERATED_BODY()
public:
	virtual void OnInitFinished();
	// 主寻路接口
	virtual TArray<FVector> FindPath(const FVector& StartWorldPos, const FVector& EndWorldPos) { return TArray<FVector>(); }
	virtual const FGridTileMap* GetTileMapData() const { return nullptr; }
	virtual void DrawDebug() const {}

protected:
	// 获取当前位置的所有可用邻居
	virtual TArray<FIntVector2> GetNeighbors(FIntVector2 Pos) const { return TArray<FIntVector2>(); }
	// 衡量两个位置之间的距离(默认采用曼哈顿距离)
	virtual int32 Distance(FIntVector2 From, FIntVector2 To) const;
public:
	UPROPERTY(BlueprintReadWrite)
	bool ShowDebugInfo = false;
};
