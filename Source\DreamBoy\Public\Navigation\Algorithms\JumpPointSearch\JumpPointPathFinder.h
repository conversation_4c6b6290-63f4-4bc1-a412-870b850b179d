#pragma once

#include "CoreMinimal.h"
//#include "UObject/NoExportTypes.h"
#include "Navigation/Core/XwNavPathFinder.h"
#include "Navigation/Algorithms/JumpPointSearch/JumpPointTypes.h"
#include "Navigation/Algorithms/JumpPointSearch/JumpPointTileMap.h"
#include "JumpPointPathFinder.generated.h"

/**
 * Jump Point Search 寻路算法实现
 */
UCLASS()
class DREAMBOY_API UJumpPointPathFinding : public UAStarPathFindingBase
{
	GENERATED_BODY()

public:
	void Initialize(const FJumpPointMap& InMap, const FPathFindingAthleticAttr& Attr);
	void InitializeWithMap(const FJumpPointMap& InMap);

	virtual TArray<FVector> FindPath(const FVector& StartWorldPos, const FVector& EndWorldPos) override;
	virtual TArray<FIntVector2> GetNeighbors(FIntVector2 Pos) const override;
	virtual int32 Distance(FIntVector2 From, FIntVector2 To) const override;

	virtual const FGridTileMap* GetTileMapData() const override { return &PathFindingMap; }


	virtual void DrawDebug() const;
protected:
	void Debug_DrawKeyPoints() const;
	void Debug_DrawConnections(const FNavPointInfo& Point) const;
	void Debug_DrawGrid(FIntVector2 Pos, FColor Color = FColor::Blue, bool bPersistentLines = false, float LifeTime = -1.f) const;
protected:
	FJumpPointMap PathFindingMap;
};