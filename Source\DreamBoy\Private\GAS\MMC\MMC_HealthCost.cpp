// Fill out your copyright notice in the Description page of Project Settings.


#include "GAS/MMC/MMC_HealthCost.h"
#include "GAS/GameAbility/GameplayAbilityBase.h"

float UMMC_HealthCost::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	const UGameplayAbilityBase* Ability = Cast<UGameplayAbilityBase>(Spec.GetContext().GetAbilityInstance_NotReplicated());

	if (!Ability)
	{
		return 0.0f;
	}

	return Ability->HealthCost.GetValueAtLevel(Ability->GetAbilityLevel());
}