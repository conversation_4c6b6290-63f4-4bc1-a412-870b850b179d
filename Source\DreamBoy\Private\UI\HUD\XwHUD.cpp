// Fill out your copyright notice in the Description page of Project Settings.

#include "UI/HUD/XwHUD.h"
#include "UI/WidgetController/OverlayWidgetController.h"
#include "UI/WidgetController/AttributeMenuWidgetController.h"
#include "UI/WidgetController/CharacterBuildWidgetController.h"
#include "UI/UserWidget/XwUserWidget.h"

UOverlayWidgetController *AXwHUD::GetOverlayWidgetController(const FWidgetControllerParams &WCParams)
{
	if (OverlayWidgetController == nullptr)
	{
		OverlayWidgetController = NewObject<UOverlayWidgetController>(this, OverlayWidgetControllerClass);
		OverlayWidgetController->SetWidgetControllerParams(WCParams);
	}
	return OverlayWidgetController;
}

UAttributeMenuWidgetController *AXwHUD::GetAttributeMenuWidgetController(const FWidgetControllerParams &WCParams)
{
	if (AttributeMenuWidgetController == nullptr)
	{
		AttributeMenuWidgetController = NewObject<UAttributeMenuWidgetController>(this, AttributeMenuWidgetControllerClass);
		AttributeMenuWidgetController->SetWidgetControllerParams(WCParams);
		AttributeMenuWidgetController->BindCallbacksToDependencies();
	}
	return AttributeMenuWidgetController;
}

UCharacterBuildWidgetController *AXwHUD::GetCharacterBuildWidgetController(const FWidgetControllerParams &WCParams)
{
	if (CharacterBuildWidgetController == nullptr)
	{
		CharacterBuildWidgetController = NewObject<UCharacterBuildWidgetController>(this, CharacterBuildWidgetControllerClass);
		CharacterBuildWidgetController->SetWidgetControllerParams(WCParams);
		CharacterBuildWidgetController->BindCallbacksToDependencies();
	}
	return CharacterBuildWidgetController;
}

void AXwHUD::InitOverlay(APlayerController *PC, APlayerState *PS, UAbilitySystemComponent *ASC, UAttributeSet *AS)
{
	checkf(OverlayWidgetClass, TEXT("Overlay Widget Class uninitialized, please fill out BP_DuraHUD"));
	checkf(OverlayWidgetControllerClass, TEXT("Overlay Widget Controller Class uninitialized, please fill out BP_DuraHUD"));

	OverlayWidget = CreateWidget<UXwUserWidget>(GetWorld(), OverlayWidgetClass);

	const FWidgetControllerParams Params(PC, PS, ASC, AS);
	UOverlayWidgetController *Controller = GetOverlayWidgetController(Params);

	OverlayWidget->SetWidgetController(Controller);
	Controller->BroadcastInitialValue();
	Controller->BindCallbacksToDependencies();
	OverlayWidget->AddToViewport();
}
