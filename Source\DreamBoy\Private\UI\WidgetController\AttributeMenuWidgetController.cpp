// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/WidgetController/AttributeMenuWidgetController.h"
#include "XwGameplayTags.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "GAS/XwAbilitySystemComponent.h"
#include "Player/XwPlayerState.h"

void UAttributeMenuWidgetController::BroadcastInitialValue()
{
	for (auto& Pair : GetXwAS()->TagsToAttributes)
	{
		BroadcastAttributeInfo(Pair.Key, Pair.Value());
	}
}

void UAttributeMenuWidgetController::BindCallbacksToDependencies()
{
	for (auto& Pair : GetXwAS()->TagsToAttributes)
	{
		AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(Pair.Value()).AddLambda(
			[this, Pair](const FOnAttributeChangeData& Data)
			{
				if (Data.NewValue != Data.OldValue)
					BroadcastAttributeInfo(Pair.Key, Pair.Value());
			}
		);
	}
}

float UAttributeMenuWidgetController::GetAttributeValue(const FGameplayTag& AttributeTag)
{
	auto Func = GetXwAS()->TagsToAttributes.Find(AttributeTag);
	if (Func)
	{
		FGameplayAttribute Attribute = (*Func)();
		return Attribute.GetNumericValue(AttributeSet);
	}
	return 0.0;
}

void UAttributeMenuWidgetController::UpgradeAttribute(const FGameplayTag& AttributeTag)
{

}

void UAttributeMenuWidgetController::BroadcastAttributeInfo(const FGameplayTag& AttributeTag, const FGameplayAttribute& Attribute) const
{
	float AttributeValue = Attribute.GetNumericValue(AttributeSet);
	AttributeInfoDelegate.Broadcast(AttributeTag, AttributeValue);
}
