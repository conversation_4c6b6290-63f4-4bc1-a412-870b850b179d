#include "Navigation/Utilities/NavigationUtilities.h"
#include "Navigation/TileMap/XwPaperTileMapActor.h"
#include "Navigation/Core/XwNavTileMap.h"
#include "PaperSpriteComponent.h"
#include "PaperSprite.h"
#include "Components/PrimitiveComponent.h"
#include "Components/BoxComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/SphereComponent.h"

bool UNavigationUtilities::CalculateOccupiedTiles(UPrimitiveComponent* Component, AXwPaperTileMapActor* TileMapActor, TArray<FIntPoint>& OutOccupiedTiles)
{
    if (!Component || !TileMapActor)
    {
        return false;
    }
    
    OutOccupiedTiles.Empty();
    
    // 获取组件的包围盒
    FBox BoundingBox = Component->Bounds.GetBox();
    
    // 获取TileMap数据
    TSharedPtr<FGridTileMap> TileMap = GetTileMapData(TileMapActor);
    if (!TileMap.IsValid())
    {
        return false;
    }
    
    // 计算包围盒覆盖的格子
    FVector Min = BoundingBox.Min;
    FVector Max = BoundingBox.Max;
    
    // 转换为格子坐标
    FIntPoint MinTile, MaxTile;
    if (!WorldToTile(Min, TileMapActor, MinTile) || !WorldToTile(Max, TileMapActor, MaxTile))
    {
        return false;
    }
    
    // 确保最小值小于最大值
    if (MinTile.X > MaxTile.X)
    {
        Swap(MinTile.X, MaxTile.X);
    }
    if (MinTile.Y > MaxTile.Y)
    {
        Swap(MinTile.Y, MaxTile.Y);
    }
    
    // 遍历包围盒覆盖的所有格子
    for (int32 X = MinTile.X; X <= MaxTile.X; ++X)
    {
        for (int32 Y = MinTile.Y; Y <= MaxTile.Y; ++Y)
        {
            if (TileMap->IsValidPos(X, Y))
            {
                OutOccupiedTiles.Add(FIntPoint(X, Y));
            }
        }
    }
    
    return OutOccupiedTiles.Num() > 0;
}

bool UNavigationUtilities::CalculateSpriteOccupiedTiles(UPaperSpriteComponent* SpriteComponent, AXwPaperTileMapActor* TileMapActor, TArray<FIntPoint>& OutOccupiedTiles)
{
    if (!SpriteComponent || !TileMapActor)
    {
        return false;
    }
    
    // 对于Sprite组件，我们可以使用更精确的计算方法
    // 但这里为了简单起见，仍然使用包围盒方法
    return CalculateOccupiedTiles(SpriteComponent, TileMapActor, OutOccupiedTiles);
}

bool UNavigationUtilities::WorldToTile(const FVector& WorldLocation, AXwPaperTileMapActor* TileMapActor, FIntPoint& OutTilePosition)
{
    if (!TileMapActor)
    {
        return false;
    }
    
    TSharedPtr<FGridTileMap> TileMap = GetTileMapData(TileMapActor);
    if (!TileMap.IsValid())
    {
        return false;
    }
    
    // 转换世界坐标到格子坐标
    FIntVector2 TilePos = TileMap->WorldToLocal(WorldLocation);
    OutTilePosition = FIntPoint(TilePos.X, TilePos.Y);
    
    return TileMap->IsValidPos(OutTilePosition.X, OutTilePosition.Y);
}

bool UNavigationUtilities::TileToWorld(const FIntPoint& TilePosition, AXwPaperTileMapActor* TileMapActor, FVector& OutWorldLocation)
{
    if (!TileMapActor)
    {
        return false;
    }
    
    TSharedPtr<FGridTileMap> TileMap = GetTileMapData(TileMapActor);
    if (!TileMap.IsValid())
    {
        return false;
    }
    
    if (!TileMap->IsValidPos(TilePosition.X, TilePosition.Y))
    {
        return false;
    }
    
    // 转换格子坐标到世界坐标
    OutWorldLocation = TileMap->LocalToWorld(FIntVector2(TilePosition.X, TilePosition.Y));
    
    return true;
}

bool UNavigationUtilities::AddDynamicObjectToTileMap(AActor* Actor, AXwPaperTileMapActor* TileMapActor, const TArray<FIntPoint>& OccupiedTiles, ENavigationTileType TileType)
{
    if (!Actor || !TileMapActor || OccupiedTiles.Num() == 0)
    {
        return false;
    }
    
    return TileMapActor->AddDynamicObject(Actor, OccupiedTiles, TileType);
}

bool UNavigationUtilities::RemoveDynamicObjectFromTileMap(AActor* Actor, AXwPaperTileMapActor* TileMapActor)
{
    if (!Actor || !TileMapActor)
    {
        return false;
    }
    
    return TileMapActor->RemoveDynamicObject(Actor);
}

TSharedPtr<FGridTileMap> UNavigationUtilities::GetTileMapData(AXwPaperTileMapActor* TileMapActor)
{
    if (!TileMapActor)
    {
        return nullptr;
    }
    
    return TileMapActor->GetBaseTileMapData();
} 