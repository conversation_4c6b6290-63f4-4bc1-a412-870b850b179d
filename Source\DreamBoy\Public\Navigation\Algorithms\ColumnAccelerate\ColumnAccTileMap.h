#pragma once

#include "CoreMinimal.h"
#include "Navigation/Algorithms/ColumnAccelerate/ColumnAccTypes.h"
#include "Navigation/Core/XwNavTileMap.h"
//#include "ColumnAccTileMap.generated.h"


struct FPathFindingMap : public FGridTileMap
{
	FPathFindingMap() {}
	virtual ~FPathFindingMap() {}
	FPathFindingMap& operator=(const FPathFindingMap& InMap);

	void RefreshCache() override;

	TArray<TArray<FPathFindingColumn>> CachedColumn;
};
