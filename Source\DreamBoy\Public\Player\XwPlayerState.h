// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerState.h"
#include "AbilitySystemInterface.h"
#include "XwPlayerState.generated.h"

class UAbilitySystemComponent;
class UAttributeSet;

DECLARE_MULTICAST_DELEGATE_OneParam(FOnPlayerStatChanged, int32 /*StatValue*/);
DECLARE_MULTICAST_DELEGATE_TwoParams(FOnLevelChanged, int32 /*StatValue*/, bool /*bLevelUp*/);

/**
 * 
 */
UCLASS()
class DREAMBOY_API AXwPlayerState : public APlayerState, public IAbilitySystemInterface
{
	GENERATED_BODY()
public:
	AXwPlayerState();

	/*--------	AbilitySystem Interface Start	--------*/
	UAbilitySystemComponent* GetAbilitySystemComponent() const override;
	/*--------	AbilitySystem Interface End		--------*/
	UAttributeSet* GetAttributeSet() const { return AttributeSet; }

	// Required XP from (Level) to (Level + 1)
	static int GetRequiredXpForLevelUp(int Level);
	static int GetRequiredXpOfLevel(int Level);
	static int GetLevelOfAbsoluteXP(int XP);
	static int GetMaxLevel() { return 100; }

	int32 GetXP() const { return XP; }
	void AddToXP(int32 InXP);
    void SetXP(int32 InXP);

	int32 GetPlayerLevel() const { return Level; }
	void AddToLevel(int32 InLevel);
	void SetLevel(int32 InLevel);

public:
	FOnPlayerStatChanged OnXPChangedDelegate;
	FOnLevelChanged OnLevelChangedDelegate;
	FOnPlayerStatChanged OnAttributePointsChangedDelegate;

protected:
	UPROPERTY(VisibleAnywhere)
	TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

	UPROPERTY()
	TObjectPtr<UAttributeSet> AttributeSet;

private:
	UPROPERTY(VisibleAnywhere);
	int32 Level = 1;
	UPROPERTY(VisibleAnywhere);
	int32 XP = 1;
};
