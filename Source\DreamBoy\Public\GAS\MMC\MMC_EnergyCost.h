// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameplayModMagnitudeCalculation.h"
#include "MMC_EnergyCost.generated.h"

/**
 * 
 */
UCLASS()
class DREAMBOY_API UMMC_EnergyCost : public UGameplayModMagnitudeCalculation
{
	GENERATED_BODY()
	
	virtual float CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const override;
};
