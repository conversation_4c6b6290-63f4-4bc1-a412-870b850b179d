# 背包收集记录系统

## 系统概述

这是一个极简的背包收集记录系统，专门用于记录玩家拥有的各种游戏实体（武器、技能、Buff等），并提供查询和统计功能。

## 核心特点

- **极简设计**：只记录拥有状态，不处理复杂的业务逻辑
- **配置驱动**：基于DataTable的配置系统，设计师友好
- **类型安全**：强类型系统，编译时检查
- **事件驱动**：支持解锁和状态变化事件
- **蓝图友好**：完整的蓝图接口支持

## 系统架构

### 核心组件

1. **UInventoryConfigDB** - 配置数据库，管理所有实体的配置数据
2. **UXwInventory** - 背包管理器，记录玩家拥有的实体
3. **UInventoryBlueprintLibrary** - 蓝图函数库，提供蓝图接口
4. **FInventoryEntity** - 运行时实体结构
5. **FInventoryEntityConfig** - 配置数据结构

### 数据流程

```
DataTable配置 -> UInventoryConfigDB -> UXwInventory -> 其他模块查询
```

## 使用方法

### 1. 配置DataTable

创建一个基于 `FInventoryEntityConfig` 的DataTable：

```cpp
// 示例配置数据
ConfigID: 1001
DisplayName: "铁剑"
Description: "一把普通的铁制长剑"
Icon: T_Sword_Iron
Type: Weapon
TypeID: 1
Rarity: Common
```

### 2. 添加背包组件

在需要背包功能的Actor上添加 `UXwInventory` 组件：

```cpp
// 在Actor的构造函数中
InventoryComponent = CreateDefaultSubobject<UXwInventory>(TEXT("InventoryComponent"));
```

### 3. 解锁实体

```cpp
// 方式1：通过ConfigID解锁
InventoryComponent->UnlockEntity(1001);

// 方式2：通过Type和TypeID解锁
InventoryComponent->UnlockEntityByType(EInventoryType::Weapon, 1);
```

### 4. 查询实体

```cpp
// 检查是否拥有
bool bHasWeapon = InventoryComponent->HasEntity(1001);
bool bHasSkill = InventoryComponent->HasEntityByType(EInventoryType::Skill, 5);

// 获取实体信息
const FInventoryEntity* Entity = InventoryComponent->GetEntityByConfigID(1001);

// 获取分类实体
TArray<FInventoryEntity> Weapons = InventoryComponent->GetWeapons();
TArray<FInventoryEntity> Skills = InventoryComponent->GetSkills();
```

### 5. 统计信息

```cpp
// 获取收集进度
float Progress = InventoryComponent->GetCollectionProgress(EInventoryType::Weapon);
int32 UnlockedCount = InventoryComponent->GetUnlockedCount(EInventoryType::Weapon);
int32 TotalCount = InventoryComponent->GetTotalCount(EInventoryType::Weapon);
bool bComplete = InventoryComponent->IsCollectionComplete(EInventoryType::Weapon);
```

### 6. 事件处理

```cpp
// 绑定解锁事件
InventoryComponent->OnEntityUnlocked.AddDynamic(this, &AMyActor::OnEntityUnlocked);

// 事件处理函数
UFUNCTION()
void OnEntityUnlocked(const FInventoryEntity& UnlockedEntity)
{
    // 处理实体解锁逻辑
}
```

## 蓝图使用

所有功能都有对应的蓝图节点：

- `Unlock Entity` - 解锁实体
- `Has Entity` - 检查是否拥有
- `Get Entities By Type` - 获取指定类型的所有实体
- `Get Collection Progress` - 获取收集进度
- `Get Inventory Type Display Name` - 获取类型显示名称

## 与其他模块的集成

### 技能系统集成示例

```cpp
// 技能系统检查是否拥有技能
if (InventoryComponent->HasEntityByType(EInventoryType::Skill, SkillTypeID))
{
    // 使用TypeID在技能系统中获取具体技能数据
    USkillData* SkillData = SkillSystem->GetSkillByTypeID(SkillTypeID);
    // 执行技能逻辑...
}
```

### 武器系统集成示例

```cpp
// 武器系统检查是否拥有武器
if (InventoryComponent->HasEntityByType(EInventoryType::Weapon, WeaponTypeID))
{
    // 使用TypeID在武器系统中获取具体武器数据
    UWeaponData* WeaponData = WeaponSystem->GetWeaponByTypeID(WeaponTypeID);
    // 装备武器逻辑...
}
```

## 存档支持

所有实体数据都标记了 `SaveGame`，会自动参与存档系统。

## 测试

使用 `AInventoryTestActor` 进行功能测试：

1. 在关卡中放置 `AInventoryTestActor`
2. 调用测试函数验证功能
3. 查看日志输出确认结果

## 注意事项

1. **配置表路径**：默认路径为 `/Game/Data/DT_InventoryEntities`，可在项目设置中修改
2. **ConfigID唯一性**：确保所有ConfigID在全局范围内唯一
3. **TypeID映射**：Type和TypeID的组合必须唯一，用于其他模块定位
4. **资源加载**：图标使用软引用，避免不必要的内存占用
5. **事件绑定**：记得在适当的时机绑定和解绑事件

## 扩展建议

1. **新增实体类型**：在 `EInventoryType` 枚举中添加新类型
2. **自定义显示信息**：扩展 `FEntityDisplayInfo` 结构
3. **过滤和排序**：在蓝图函数库中添加过滤和排序功能
4. **批量操作**：添加批量解锁和查询接口
