// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "XwGameModeBase.generated.h"

class UAStarPathFindingBase;
class UXwAStarPathFinding;
class UJ<PERSON>PointPathFinding;
class <PERSON>haracterBase;

/**
 * 
 */
UCLASS()
class DREAMBOY_API AXwGameModeBase : public AGameModeBase
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable)
	UAStarPathFindingBase* GeneratePathFindObjectForCharacter(ACharacterBase* Character, UObject* ObjectOwner);
protected:
	virtual void StartPlay() override;
	virtual void BeginPlay() override;

	//UPROPERTY(BlueprintReadOnly)
	//TObjectPtr<UXwAStarPathFinding> XwAStarPathFinding;
};
