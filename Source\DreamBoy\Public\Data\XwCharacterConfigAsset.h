// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "Character/CharacterBase.h"
#include "XwCharacterConfigAsset.generated.h"

class UGameplayEffect;
class UPaperZDAnimInstance;
class UPaperZDAnimSequence;

/**
 * 
 */
UCLASS()
class DREAMBOY_API UXwCharacterConfigAsset : public UDataAsset
{
	GENERATED_BODY()
public:
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_MaxHealth;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_MaxMana;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_MaxEnergy;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_Attack;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_Defense;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_CriticalHitChance;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_CriticalHitDamage;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_CriticalHitResistance;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_ImmuneRate;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_AttackSpeed;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	float BasicValue_MoveSpeed;

	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	TSubclassOf<UGameplayEffect> PrimaryInitEffectClass;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	TSubclassOf<UGameplayEffect> SecondaryInitEffectClass;
	UPROPERTY(EditDefaultsOnly, Category = "Attributes")
	TSubclassOf<UGameplayEffect> VitalInitEffectClass;

	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	TSubclassOf<UPaperZDAnimInstance> AnimInstanceClass;

	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* IdleAnimSQ;
	//UPROPERTY(EditDefaultsOnly, Category = "Animation")
	//UPaperZDAnimSequence MoveAnimSQ;
	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* RunAnimSQ;
	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* StopAnimSQ;
	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* JumpStartAnimSQ;
	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* JumpingAnimSQ;
	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* FallingAnimSQ;
	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* GuardAnimSQ;
	UPROPERTY(EditDefaultsOnly, Category = "Animation")
	UPaperZDAnimSequence* DeadAnimSQ;

	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	FAnimateAbilityPair DashConfig;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	TArray<FAnimateAbilityPair> ComboConfig;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	FAnimateAbilityPair ChargeConfig;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	TArray<FAnimateAbilityPair> SkillConfig;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	FAnimateAbilityPair ItemConfig;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	FAnimateAbilityPair HitConfig;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	FAnimateAbilityPair KnockBackConfig;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	FAnimateAbilityPair DeadConfig;

	//UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	//TMap<FName, FAnimateAbilityPair> RequiredAbilityConfigs;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	TArray<FAnimateAbilityPair> AdditionalAbilityConfigs;
	UPROPERTY(EditDefaultsOnly, Category = "Abilities")
	TArray<TSubclassOf<UGameplayAbility>> PassiveAbilityConfigs;


	UPROPERTY(EditDefaultsOnly, Category = "Movement")
	float DefaultMoveSpeed;

};
