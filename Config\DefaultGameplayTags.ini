[/Script/GameplayTags.GameplayTagsSettings]
ImportTagsFromConfig=True
WarnOnInvalidTags=True
ClearInvalidTags=False
AllowEditorTagUnloading=True
AllowGameTagUnloading=False
FastReplication=False
InvalidTagCharacters="\"\',"
+GameplayTagRedirects=(OldTagName="State.Input.Reactionable",NewTagName="InputWindow")
+GameplayTagRedirects=(OldTagName="State.death",NewTagName="State.Dead")
+GameplayTagRedirects=(OldTagName="State.PefectGuard",NewTagName="State.PerfectGuard")
+GameplayTagRedirects=(OldTagName="Ability.WrathCombo",NewTagName="Ability.Skill.WrathCombo")
NumBitsForContainerSize=6
NetIndexFirstBitSegment=16
+GameplayTagList=(Tag="Ability.Appear",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Normal",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Normal.1",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Normal.2",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Normal.3",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Range",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Special",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Special.1",DevComment="")
+GameplayTagList=(Tag="Ability.Attack.Special.2",DevComment="")
+GameplayTagList=(Tag="Ability.Block",DevComment="")
+GameplayTagList=(Tag="Ability.Dash",DevComment="")
+GameplayTagList=(Tag="Ability.Dead",DevComment="")
+GameplayTagList=(Tag="Ability.Disappear",DevComment="")
+GameplayTagList=(Tag="Ability.Dodge",DevComment="")
+GameplayTagList=(Tag="Ability.FollowPlayer",DevComment="")
+GameplayTagList=(Tag="Ability.Guard",DevComment="")
+GameplayTagList=(Tag="Ability.Hit.Normal",DevComment="")
+GameplayTagList=(Tag="Ability.Hit.NotAnimation",DevComment="")
+GameplayTagList=(Tag="Ability.Jump",DevComment="")
+GameplayTagList=(Tag="Ability.Jump.Normal",DevComment="")
+GameplayTagList=(Tag="Ability.Jump.Wall",DevComment="")
+GameplayTagList=(Tag="Ability.KnockBack",DevComment="")
+GameplayTagList=(Tag="Ability.NotReceiveDamage",DevComment="")
+GameplayTagList=(Tag="Ability.Patrol",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.FloatingBed",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.PrideBurst",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.ThrowHeart",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.WrathBuff",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.WrathCombo",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.WrathCombo.1",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.WrathCombo.2",DevComment="")
+GameplayTagList=(Tag="Ability.Skill.WrathCombo.3",DevComment="")
+GameplayTagList=(Tag="Attribute.BaseAttack",DevComment="")
+GameplayTagList=(Tag="Attribute.Health",DevComment="")
+GameplayTagList=(Tag="Block.Input.All",DevComment="")
+GameplayTagList=(Tag="Block.Input.Combo.1",DevComment="")
+GameplayTagList=(Tag="Block.Input.Combo.2",DevComment="")
+GameplayTagList=(Tag="Block.Input.Combo.3",DevComment="")
+GameplayTagList=(Tag="Block.Input.Dash",DevComment="")
+GameplayTagList=(Tag="Block.Input.Item",DevComment="")
+GameplayTagList=(Tag="Block.Input.Jump",DevComment="")
+GameplayTagList=(Tag="Block.Input.Move",DevComment="")
+GameplayTagList=(Tag="Block.Input.Skill",DevComment="")
+GameplayTagList=(Tag="Character.AI.Enemy",DevComment="")
+GameplayTagList=(Tag="Character.AI.Enemy.NotMove",DevComment="")
+GameplayTagList=(Tag="Character.Player.1",DevComment="")
+GameplayTagList=(Tag="CoolDown.Dash",DevComment="")
+GameplayTagList=(Tag="CoolDown.Skill.FloatingBed",DevComment="")
+GameplayTagList=(Tag="CoolDown.Skill.ThrowHeart",DevComment="")
+GameplayTagList=(Tag="Event.Falling",DevComment="")
+GameplayTagList=(Tag="Event.Hit",DevComment="")
+GameplayTagList=(Tag="Event.KnockBack",DevComment="")
+GameplayTagList=(Tag="GameplayCue.DamageText",DevComment="")
+GameplayTagList=(Tag="GameplayCue.HitSpark",DevComment="")
+GameplayTagList=(Tag="GameplayCue.WrathBuff",DevComment="")
+GameplayTagList=(Tag="InputWindow",DevComment="")
+GameplayTagList=(Tag="InputWindow.All",DevComment="")
+GameplayTagList=(Tag="InputWindow.Attack",DevComment="")
+GameplayTagList=(Tag="InputWindow.Dash",DevComment="")
+GameplayTagList=(Tag="InputWindow.Item",DevComment="")
+GameplayTagList=(Tag="InputWindow.Jump",DevComment="")
+GameplayTagList=(Tag="InputWindow.Move",DevComment="")
+GameplayTagList=(Tag="InputWindow.Skill",DevComment="")
+GameplayTagList=(Tag="Message.Action.Attack",DevComment="")
+GameplayTagList=(Tag="Message.Camera.Stop",DevComment="")
+GameplayTagList=(Tag="Message.ChangeAttackTraceSize",DevComment="修改攻击检测体积大小")
+GameplayTagList=(Tag="Message.DestroyOldSword",DevComment="")
+GameplayTagList=(Tag="State.AboutToLand",DevComment="")
+GameplayTagList=(Tag="State.Appear",DevComment="AI出现在场景里")
+GameplayTagList=(Tag="State.Attack.Combo.1",DevComment="")
+GameplayTagList=(Tag="State.Attack.Combo.2",DevComment="")
+GameplayTagList=(Tag="State.Attack.Combo.3",DevComment="")
+GameplayTagList=(Tag="State.Attack.Normal",DevComment="")
+GameplayTagList=(Tag="State.Attack.Range",DevComment="")
+GameplayTagList=(Tag="State.Attack.Special",DevComment="")
+GameplayTagList=(Tag="State.Block",DevComment="")
+GameplayTagList=(Tag="State.Buff.AttackSpeed",DevComment="")
+GameplayTagList=(Tag="State.Dash",DevComment="")
+GameplayTagList=(Tag="State.Dash.Immune",DevComment="")
+GameplayTagList=(Tag="State.Dead",DevComment="")
+GameplayTagList=(Tag="State.Disappear",DevComment="")
+GameplayTagList=(Tag="State.Dodge",DevComment="")
+GameplayTagList=(Tag="State.Enable.Dash",DevComment="")
+GameplayTagList=(Tag="State.Enable.Jump",DevComment="")
+GameplayTagList=(Tag="State.FollowPlayer",DevComment="")
+GameplayTagList=(Tag="State.FoundTarget.ByDamage",DevComment="")
+GameplayTagList=(Tag="State.FoundTarget.ByHear",DevComment="")
+GameplayTagList=(Tag="State.FoundTarget.BySight",DevComment="")
+GameplayTagList=(Tag="State.Guard",DevComment="")
+GameplayTagList=(Tag="State.Hit.Normal",DevComment="")
+GameplayTagList=(Tag="State.Idle",DevComment="")
+GameplayTagList=(Tag="State.Item",DevComment="")
+GameplayTagList=(Tag="State.Jump",DevComment="")
+GameplayTagList=(Tag="State.NotReceiveDamage",DevComment="")
+GameplayTagList=(Tag="State.Patrol",DevComment="")
+GameplayTagList=(Tag="State.PerfectGuard",DevComment="")
+GameplayTagList=(Tag="State.PlayingAniamtion",DevComment="")
+GameplayTagList=(Tag="State.Skill.WrathCombo.1",DevComment="")
+GameplayTagList=(Tag="State.Skill.WrathCombo.2",DevComment="")
+GameplayTagList=(Tag="State.Skill.WrathCombo.3",DevComment="")
+GameplayTagList=(Tag="State.Walk",DevComment="")
+GameplayTagList=(Tag="Status.Attacking",DevComment="")
+GameplayTagList=(Tag="Status.NoDamage",DevComment="")
+GameplayTagList=(Tag="Status.NotHitAnimation",DevComment="")
+GameplayTagList=(Tag="Status.PlayerSeen",DevComment="")
+GameplayTagList=(Tag="Status.PlayingAnimation",DevComment="")

