// Fill out your copyright notice in the Description page of Project Settings.


#include "GAS/MMC/Attribute/MMC_Attribute_CriticalHitResistance.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "Interaction/CombatInterface.h"
#include "Character/CharacterBase.h"
#include "Data/XwCharacterConfigAsset.h"

UMMC_Attribute_CriticalHitResistance::UMMC_Attribute_CriticalHitResistance()
{
	ConstitutionDef.AttributeToCapture = UAttributeSetBase::GetConstitutionAttribute();
	ConstitutionDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
	ConstitutionDef.bSnapshot = false;

	RelevantAttributesToCapture.Add(ConstitutionDef);
}

float UMMC_Attribute_CriticalHitResistance::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	// Gather tags from source and target
	const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
	const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluationParameters;
	EvaluationParameters.SourceTags = SourceTags;
	EvaluationParameters.TargetTags = TargetTags;

	float Constitution = 0.f;
	GetCapturedAttributeMagnitude(ConstitutionDef, Spec, EvaluationParameters, Constitution);
	Constitution = FMath::Max<float>(Constitution, 0.f);

	//int32 PlayerLevel = 1;
	//if (Spec.GetContext().GetSourceObject()->Implements<UCombatInterface>())
	//{
	//	PlayerLevel = ICombatInterface::Execute_GetPlayerLevel(Spec.GetContext().GetSourceObject());
	//}
	float BasicValue = 0.0;
	if (ACharacterBase* Character = Cast<ACharacterBase>(Spec.GetContext().GetSourceObject()))
	{
		if (UXwCharacterConfigAsset* ConfigAsset = Character->GetConfigAsset())
		{
			BasicValue = ConfigAsset->BasicValue_CriticalHitResistance;
		}
	}
	

	return BasicValue + 0.01 * Constitution;
}
