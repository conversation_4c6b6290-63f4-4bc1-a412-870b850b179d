// Fill out your copyright notice in the Description page of Project Settings.


#include "GAS/MMC/Attribute/MMC_Attribute_CriticalHitChance.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "Interaction/CombatInterface.h"
#include "Character/CharacterBase.h"
#include "Data/XwCharacterConfigAsset.h"

UMMC_Attribute_CriticalHitChance::UMMC_Attribute_CriticalHitChance()
{
	AgilityDef.AttributeToCapture = UAttributeSetBase::GetAgilityAttribute();
	AgilityDef.AttributeSource = EGameplayEffectAttributeCaptureSource::Target;
	AgilityDef.bSnapshot = false;

	RelevantAttributesToCapture.Add(AgilityDef);
}

float UMMC_Attribute_CriticalHitChance::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	// Gather tags from source and target
	const FGameplayTagContainer* SourceTags = Spec.CapturedSourceTags.GetAggregatedTags();
	const FGameplayTagContainer* TargetTags = Spec.CapturedTargetTags.GetAggregatedTags();

	FAggregatorEvaluateParameters EvaluationParameters;
	EvaluationParameters.SourceTags = SourceTags;
	EvaluationParameters.TargetTags = TargetTags;

	float Agility = 0.f;
	GetCapturedAttributeMagnitude(AgilityDef, Spec, EvaluationParameters, Agility);
	Agility = FMath::Max<float>(Agility, 0.f);

	//int32 PlayerLevel = 1;
	//if (Spec.GetContext().GetSourceObject()->Implements<UCombatInterface>())
	//{
	//	PlayerLevel = ICombatInterface::Execute_GetPlayerLevel(Spec.GetContext().GetSourceObject());
	//}
	float BasicValue = 0.0;
	if (ACharacterBase* Character = Cast<ACharacterBase>(Spec.GetContext().GetSourceObject()))
	{
		if (UXwCharacterConfigAsset* ConfigAsset = Character->GetConfigAsset())
		{
			BasicValue = ConfigAsset->BasicValue_CriticalHitChance;
		}
	}
	

	return BasicValue + 0.01 * Agility;
}
