// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "XwAbilitySystemLibrary.generated.h"

struct FWidgetControllerParams;
class AXwHUD;
class UOverlayWidgetController;
class UAttributeMenuWidgetController;
class UCharacterBuildWidgetController;
class ACharacter;
class ACharacterBase;

/**
 *
 */
UCLASS()
class DREAMBOY_API UXwAbilitySystemLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	/* Attribute */
	UFUNCTION(BlueprintPure, Category = "XwAbilitySystemLibrary|Attribute", meta = (DefaultToSelf = "WorldContextObject"))
	static float GetCharacterAttributeByTag(const UObject *WorldContextObject, ACharacterBase *Character, FGameplayTag Tag);
	UFUNCTION(BlueprintPure, Category = "XwAbilitySystemLibrary|Attribute", meta = (DefaultToSelf = "WorldContextObject"))
	static int GetCharacterLevel(const UObject *WorldContextObject, AActor *Character);

	/* Widget Controller */

	UFUNCTION(BlueprintPure, Category = "XwAbilitySystemLibrary|WidgetController", meta = (DefaultToSelf = "WorldContextObject"))
	static bool MakeWidgetControllerParams(const UObject *WorldContextObject,
										   FWidgetControllerParams &OutWCParams, AXwHUD *&OutXwHUD);

	UFUNCTION(BlueprintPure, Category = "XwAbilitySystemLibrary|WidgetController", meta = (DefaultToSelf = "WorldContextObject"))
	static UOverlayWidgetController *GetOverlayWidgetController(const UObject *WorldContextObject);

	UFUNCTION(BlueprintPure, Category = "XwAbilitySystemLibrary|WidgetController", meta = (DefaultToSelf = "WorldContextObject"))
	static UAttributeMenuWidgetController *GetAttributeMenuWidgetController(const UObject *WorldContextObject);

	UFUNCTION(BlueprintPure, Category = "XwAbilitySystemLibrary|WidgetController", meta = (DefaultToSelf = "WorldContextObject"))
	static UCharacterBuildWidgetController *GetCharacterBuildWidgetController(const UObject *WorldContextObject);

	// UFUNCTION(BlueprintPure, Category = "XwAbilitySystemLibrary|WidgetController", meta = (DefaultToSelf = "WorldContextObject"))
	// static USpellMenuWidgetController* GetSpellMenuWidgetController(const UObject* WorldContextObject);

	UFUNCTION(BlueprintCallable, Category = "XwAbilitySystemLibrary|DamageEffect")
	static FGameplayEffectContextHandle ApplyDamageEffect(const FDamageEffectParams &DamageEffectParams);
};
