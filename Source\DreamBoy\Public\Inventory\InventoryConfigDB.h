// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Inventory/InventoryTypes.h"
#include "InventoryConfigDB.generated.h"

// 前向声明
class UDataTable;

/**
 * 背包配置数据库
 * 负责管理所有背包实体的配置数据
 */
UCLASS()
class DREAMBOY_API UInventoryConfigDB : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    /**
     * 获取实体配置数据
     * @param ConfigID 配置ID
     * @return 配置数据指针，如果不存在则返回nullptr
     */
    //UFUNCTION(BlueprintCallable, Category = "Inventory|Config")
    const FInventoryEntityConfig* GetEntityConfig(int32 ConfigID) const;

    /**
     * 通过Type和TypeID获取ConfigID
     * @param Type 实体类型
     * @param TypeID 类型内ID
     * @return ConfigID，如果不存在则返回0
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory|Config")
    int32 GetConfigIDByType(EInventoryType Type, int32 TypeID) const;

    /**
     * 验证ConfigID是否有效
     * @param ConfigID 要验证的配置ID
     * @return 是否有效
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory|Config")
    bool ValidateConfigID(int32 ConfigID) const;

    /**
     * 获取指定类型的所有配置
     * @param Type 实体类型
     * @return 配置数据数组
     */
    //UFUNCTION(BlueprintCallable, Category = "Inventory|Config")
    TArray<const FInventoryEntityConfig*> GetAllConfigsByType(EInventoryType Type) const;

    /**
     * 获取指定类型的配置数量
     * @param Type 实体类型
     * @return 配置数量
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory|Config")
    int32 GetConfigCountByType(EInventoryType Type) const;

protected:
    /**
     * 加载配置表
     */
    void LoadConfigTable();

    /**
     * 构建Type和TypeID到ConfigID的映射表
     */
    void BuildTypeToConfigMap();

    /**
     * 清理缓存数据
     */
    void ClearCache();

private:
    /** 配置数据表资源路径 */
    UPROPERTY(EditDefaultsOnly, Category = "Config")
    TSoftObjectPtr<UDataTable> ConfigTableAsset;

    /** 加载的配置数据表 */
    UPROPERTY()
    TObjectPtr<UDataTable> ConfigTable;

    /** 配置数据缓存 ConfigID -> Config */
    TMap<int32, const FInventoryEntityConfig*> ConfigCache;

    /** Type和TypeID到ConfigID的映射表 Type -> (TypeID -> ConfigID) */
    TMap<EInventoryType, TMap<int32, int32>> TypeToConfigMap;

    /** 按类型分组的配置缓存 Type -> Configs */
    TMap<EInventoryType, TArray<const FInventoryEntityConfig*>> ConfigsByType;

public:
    /**
     * 获取配置数据库实例
     * @param WorldContext 世界上下文
     * @return 配置数据库实例
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory|Config", meta = (DefaultToSelf = "WorldContext"))
    static UInventoryConfigDB* GetInventoryConfigDB(const UObject* WorldContext);
};
