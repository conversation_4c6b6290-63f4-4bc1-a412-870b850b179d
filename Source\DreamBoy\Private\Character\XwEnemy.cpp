// Fill out your copyright notice in the Description page of Project Settings.


#include "Character/XwEnemy.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "Components/WidgetComponent.h"
#include "PaperFlipbookComponent.h"
#include "XwGameplayTags.h"
#include "UI/UserWidget/XwUserWidget.h"
#include "Components/CapsuleComponent.h"
#include "Data/EnemyConfigAsset.h"
#include "AIController.h"
#include "Navigation/Components/EnemyNavigationComponent.h"

AXwEnemy::AXwEnemy()
{
	Tags.Add("Enemy");
	GetCapsuleComponent()->SetCollisionProfileName(TEXT("XwEnemy"));

	AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>("AbilitySystemComponent");
	AbilitySystemComponent->SetIsReplicated(false);

	AttributeSet = CreateDefaultSubobject<UAttributeSetBase>("AttributeSet");

	UIWidget = CreateDefaultSubobject<UWidgetComponent>("UIWidget");
	UIWidget->SetupAttachment(GetRootComponent());
	UIWidget->SetUsingAbsoluteRotation(true);
	UIWidget->SetWorldRotation(FRotator(0, 90, 0));

	// 创建导航组件
	NavigationComponent = CreateDefaultSubobject<UEnemyNavigationComponent>("NavigationComponent");

	AutoPossessAI = EAutoPossessAI::PlacedInWorldOrSpawned;
}

int32 AXwEnemy::GetPlayerLevel_Implementation() const
{
	return Level;
}

UAStarPathFindingBase* AXwEnemy::GetPathFinder() const
{
	if (NavigationComponent)
	{
		return NavigationComponent->GetPathFinder();
	}
	return nullptr;
}

void AXwEnemy::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);
	if (!UIWidget) return;

	FVector2D WidgetSize = UIWidget->GetCurrentDrawSize();
	float WidgetZ = GetCapsuleComponent()->GetScaledCapsuleHalfHeight() + WidgetSize.Y / 2 + 5; 
	UIWidget->SetRelativeLocation(FVector(0, 0, WidgetZ));
}

void AXwEnemy::BeginPlay()
{
	Super::BeginPlay();
	InitReferences();
	InitAbilities();
	InitializeDefaultAttributes();
	InitUI();
	InitNavigation();
}

void AXwEnemy::BeginDestroy()
{
	Super::BeginDestroy();
}

void AXwEnemy::PossessedBy(AController* NewController)
{
	Super::PossessedBy(NewController);

	AAIController* AIC = Cast<AAIController>(NewController);
	if (AIC)
	{
		AIC->RunBehaviorTree(BehaviorTree);
	}
}

bool AXwEnemy::LoadConfigAsset()
{
	if(!Super::LoadConfigAsset()) return false;

	UEnemyConfigAsset* EnemyConfigAsset = Cast<UEnemyConfigAsset>(ConfigAsset);
	if(!EnemyConfigAsset) return false;

	Race = EnemyConfigAsset->Race;
	ObtainXP = EnemyConfigAsset->ObtainXP;
	UIWidget->SetWidgetClass(EnemyConfigAsset->UIWidgetClass);
	AIControllerClass = EnemyConfigAsset->AIControllerClass;
	BehaviorTree = EnemyConfigAsset->BehaviorTree;

	return true;
}

void AXwEnemy::InitReferences()
{
	AbilitySystemComponent->InitAbilityActorInfo(this, this);
}

void AXwEnemy::InitUI()
{
	if (UXwUserWidget* XwUserWidget = Cast<UXwUserWidget>(UIWidget->GetUserWidgetObject()))
	{
		XwUserWidget->SetWidgetController(this);
	}

	if (UAttributeSetBase* AS = Cast<UAttributeSetBase>(AttributeSet))
	{
		AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetHealthAttribute()).AddLambda(
			[this](const FOnAttributeChangeData& Data)
			{
				OnHealthChanged.Broadcast(Data.NewValue);
			}
		);
		AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AS->GetMaxHealthAttribute()).AddLambda(
			[this](const FOnAttributeChangeData& Data)
			{
				OnMaxHealthChanged.Broadcast(Data.NewValue);
			}
		);

		//AbilitySystemComponent->RegisterGameplayTagEvent(FXwGameplayTags::Get().Effect_HitReact,
		//	EGameplayTagEventType::NewOrRemoved).AddUObject(this, &AXwEnemy::HitReactTagChanged);

		//OnHealthChanged.Broadcast(AS->GetHealth());
		//OnMaxHealthChanged.Broadcast(AS->GetMaxHealth());
	}
}

void AXwEnemy::InitNavigation()
{
	// 初始化导航组件
	if (NavigationComponent && Race != EEnemyRace::None)
	{
		NavigationComponent->InitializeNavigation(Race);
	}
}
