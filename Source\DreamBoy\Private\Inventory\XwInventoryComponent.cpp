// Fill out your copyright notice in the Description page of Project Settings.

#include "Inventory/XwInventoryComponent.h"
#include "Inventory/InventoryConfigDB.h"

UXwInventoryComponent::UXwInventoryComponent()
{
    PrimaryComponentTick.bCanEverTick = false;
    bWantsInitializeComponent = true;
}

void UXwInventoryComponent::BeginPlay()
{
    Super::BeginPlay();
    InitializeConfigDB();
}

bool UXwInventoryComponent::UnlockEntity(int32 ConfigID)
{
    if (!ConfigDB)
    {
        UE_LOG(LogTemp, Error, TEXT("UXwInventoryComponent::UnlockEntity - ConfigDB is null"));
        return false;
    }

    // 验证ConfigID
    const FInventoryEntityConfig* Config = ConfigDB->GetEntityConfig(ConfigID);
    if (!Config)
    {
        UE_LOG(LogTemp, Warning, TEXT("UXwInventoryComponent::UnlockEntity - Invalid ConfigID: %d"), ConfigID);
        return false;
    }

    // 检查是否已经拥有
    if (HasEntity(ConfigID))
    {
        UE_LOG(LogTemp, Log, TEXT("UXwInventoryComponent::UnlockEntity - Entity %d already unlocked"), ConfigID);
        return false;
    }

    // 获取对应的存储数组
    TArray<FInventoryEntity>* StorageArray = GetStorageArrayByType(Config->Type);
    if (!StorageArray)
    {
        UE_LOG(LogTemp, Error, TEXT("UXwInventoryComponent::UnlockEntity - Invalid entity type: %d"), (int32)Config->Type);
        return false;
    }

    // 创建新实体
    FInventoryEntity NewEntity(ConfigID, Config->Type, Config->TypeID);
    StorageArray->Add(NewEntity);

    // 广播事件
    OnEntityUnlocked.Broadcast(NewEntity);
    OnEntityStatusChanged.Broadcast(NewEntity, true);

    UE_LOG(LogTemp, Log, TEXT("UXwInventoryComponent::UnlockEntity - Successfully unlocked entity %d"), ConfigID);
    return true;
}

bool UXwInventoryComponent::UnlockEntityByType(EInventoryType Type, int32 TypeID)
{
    if (!ConfigDB)
    {
        UE_LOG(LogTemp, Error, TEXT("UXwInventoryComponent::UnlockEntityByType - ConfigDB is null"));
        return false;
    }

    // 通过Type和TypeID获取ConfigID
    int32 ConfigID = ConfigDB->GetConfigIDByType(Type, TypeID);
    if (ConfigID <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("UXwInventoryComponent::UnlockEntityByType - No config found for Type %d, TypeID %d"), 
               (int32)Type, TypeID);
        return false;
    }

    return UnlockEntity(ConfigID);
}

bool UXwInventoryComponent::HasEntity(int32 ConfigID) const
{
    return GetEntityByConfigID(ConfigID) != nullptr;
}

bool UXwInventoryComponent::HasEntityByType(EInventoryType Type, int32 TypeID) const
{
    return GetEntityByType(Type, TypeID) != nullptr;
}

const FInventoryEntity* UXwInventoryComponent::GetEntityByConfigID(int32 ConfigID) const
{
    // 遍历所有存储数组查找
    const TArray<EInventoryType> AllTypes = {
        EInventoryType::Weapon,
        EInventoryType::Armor,
        EInventoryType::Accessory,
        EInventoryType::Item,
        EInventoryType::Skill,
        EInventoryType::Buff,
        EInventoryType::ImportantItem
    };

    for (EInventoryType Type : AllTypes)
    {
        const TArray<FInventoryEntity>* StorageArray = GetStorageArrayByType(Type);
        if (StorageArray)
        {
            for (const FInventoryEntity& Entity : *StorageArray)
            {
                if (Entity.ConfigID == ConfigID)
                {
                    return &Entity;
                }
            }
        }
    }

    return nullptr;
}


FInventoryEntity UXwInventoryComponent::K2_GetEntityByConfigID(int32 ConfigID) const
{
	if (auto* Entity = GetEntityByConfigID(ConfigID))
	{
		return *Entity;
	}
	else
	{
		return FInventoryEntity();
	}
}

const FInventoryEntity* UXwInventoryComponent::GetEntityByType(EInventoryType Type, int32 TypeID) const
{
    const TArray<FInventoryEntity>* StorageArray = GetStorageArrayByType(Type);
    if (!StorageArray)
    {
        return nullptr;
    }

    for (const FInventoryEntity& Entity : *StorageArray)
    {
        if (Entity.TypeID == TypeID)
        {
            return &Entity;
        }
    }

    return nullptr;
}


FInventoryEntity UXwInventoryComponent::K2_GetEntityByType(EInventoryType Type, int32 TypeID) const
{
	if (auto* Entity = GetEntityByType(Type, TypeID))
    {
        return *Entity;
	}
    else
	{
		return FInventoryEntity();
    }
}

TArray<FInventoryEntity> UXwInventoryComponent::GetEntitiesByType(EInventoryType Type) const
{
    const TArray<FInventoryEntity>* StorageArray = GetStorageArrayByType(Type);
    if (StorageArray)
    {
        return *StorageArray;
    }

    return TArray<FInventoryEntity>();
}

int32 UXwInventoryComponent::GetUnlockedCount(EInventoryType Type) const
{
    const TArray<FInventoryEntity>* StorageArray = GetStorageArrayByType(Type);
    return StorageArray ? StorageArray->Num() : 0;
}

int32 UXwInventoryComponent::GetTotalCount(EInventoryType Type) const
{
    if (ConfigDB)
    {
        return ConfigDB->GetConfigCountByType(Type);
    }

    return 0;
}

bool UXwInventoryComponent::IsCollectionComplete(EInventoryType Type) const
{
    int32 UnlockedCount = GetUnlockedCount(Type);
    int32 TotalCount = GetTotalCount(Type);
    return TotalCount > 0 && UnlockedCount >= TotalCount;
}

float UXwInventoryComponent::GetCollectionProgress(EInventoryType Type) const
{
    int32 TotalCount = GetTotalCount(Type);
    if (TotalCount <= 0)
    {
        return 0.0f;
    }

    int32 UnlockedCount = GetUnlockedCount(Type);
    return FMath::Clamp((float)UnlockedCount / (float)TotalCount, 0.0f, 1.0f);
}

void UXwInventoryComponent::MarkEntityAsViewed(int32 ConfigID)
{
    FInventoryEntity* Entity = const_cast<FInventoryEntity*>(GetEntityByConfigID(ConfigID));
    if (Entity && Entity->bIsNew)
    {
        Entity->bIsNew = false;
        OnEntityStatusChanged.Broadcast(*Entity, false);
    }
}

void UXwInventoryComponent::MarkAllEntitiesAsViewed(EInventoryType Type)
{
    TArray<FInventoryEntity>* StorageArray = GetStorageArrayByType(Type);
    if (!StorageArray)
    {
        return;
    }

    for (FInventoryEntity& Entity : *StorageArray)
    {
        if (Entity.bIsNew)
        {
            Entity.bIsNew = false;
            OnEntityStatusChanged.Broadcast(Entity, false);
        }
    }
}

void UXwInventoryComponent::InitializeConfigDB()
{
    ConfigDB = UInventoryConfigDB::GetInventoryConfigDB(this);
    if (!ConfigDB)
    {
        UE_LOG(LogTemp, Error, TEXT("UXwInventoryComponent::InitializeConfigDB - Failed to get InventoryConfigDB"));
    }
}

TArray<FInventoryEntity>* UXwInventoryComponent::GetStorageArrayByType(EInventoryType Type)
{
    switch (Type)
    {
    case EInventoryType::Weapon:
        return &Weapons;
    case EInventoryType::Armor:
        return &Armors;
    case EInventoryType::Accessory:
        return &Accessories;
    case EInventoryType::Item:
        return &Items;
    case EInventoryType::Skill:
        return &Skills;
    case EInventoryType::Buff:
        return &Buffs;
    case EInventoryType::ImportantItem:
        return &ImportantItems;
    default:
        return nullptr;
    }
}

const TArray<FInventoryEntity>* UXwInventoryComponent::GetStorageArrayByType(EInventoryType Type) const
{
    return const_cast<UXwInventoryComponent*>(this)->GetStorageArrayByType(Type);
}
