// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Character/CharacterDefines.h"
#include "Character/CharacterBase.h"
#include "UI/WidgetController/OverlayWidgetController.h"
#include "XwEnemy.generated.h"

class AController;
class UWidgetComponent;
class UBehaviorTree;
class UAStarPathFindingBase;
class UEnemyNavigationComponent;

/**
 * 敌人基类
 */
UCLASS()
class DREAMBOY_API AXwEnemy : public ACharacterBase
{
	GENERATED_BODY()
	
public:
	AXwEnemy();

	/*--------	Combat Interface Start	--------*/
	virtual int32 GetPlayerLevel_Implementation() const override;
	/*--------	Combat Interface End	--------*/

	/**
	 * 获取导航组件
	 * @return 导航组件
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation")
	UEnemyNavigationComponent* GetNavigationComponent() const { return NavigationComponent; }

	/**
	 * 获取寻路对象
	 * @return 寻路对象
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation")
	UAStarPathFindingBase* GetPathFinder() const;

protected:
	virtual void OnConstruction(const FTransform& Transform) override;
	virtual void BeginPlay() override;
	virtual void BeginDestroy() override;
	virtual void PossessedBy(AController* NewController) override;

	void OnTileMapInited();
private:
	virtual bool LoadConfigAsset() override;
	virtual void InitReferences() override;
	void InitUI();
	void InitNavigation();

public:
	UPROPERTY(BlueprintAssignable)
	FOnAttributeChangedSignature OnHealthChanged;

	UPROPERTY(BlueprintAssignable)
	FOnAttributeChangedSignature OnMaxHealthChanged;

	UPROPERTY(BlueprintReadOnly)
	EEnemyRace Race;

	UPROPERTY(BlueprintReadOnly)
	int ObtainXP;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Character Class Defaults");
	int32 Level = 1;

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	TObjectPtr<UWidgetComponent> UIWidget;

	UPROPERTY()
	TObjectPtr<UBehaviorTree> BehaviorTree;

	/** 导航组件 */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Navigation")
	TObjectPtr<UEnemyNavigationComponent> NavigationComponent;
};
