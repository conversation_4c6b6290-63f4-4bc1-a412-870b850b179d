// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Character/CharacterDefines.h"
#include "GridTileMapManager.generated.h"

struct FGridTileMap;
struct FJumpPointMap;
class AActor;
class AXwPaperTileMapActor;
class ACharacterBase;

/**
 * TileMap管理器
 * 负责管理所有TileMap和导航网格
 */
UCLASS()
class DREAMBOY_API AGridTileMapManager : public AActor
{
	GENERATED_BODY()
public:
	virtual void BeginPlay() override;
	virtual void BeginDestroy() override;

	void Initialze();
	void DeInitialze();

	/**
	 * 根据世界坐标查找TileMap
	 * @param WorldLocation 世界坐标
	 * @return 包含该坐标的TileMapActor，如果没有找到则返回nullptr
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	AXwPaperTileMapActor *FindTileMapByLocation(FVector WorldLocation);

	/**
	 * 注册TileMap
	 * @param TileMapActor 要注册的TileMapActor
	 * @return 是否注册成功
	 */
	// UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	bool RegisterTileMap(AXwPaperTileMapActor *TileMapActor);

	/**
	 * 注销TileMap
	 * @param TileMapActor 要注销的TileMapActor
	 */
	// UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	void UnRegisterTileMap(AXwPaperTileMapActor *TileMapActor);

	/**
	 * 为角色注册TileMap
	 * @param Character 角色
	 * @param EnemyTypeID 敌人类型
	 * @return 是否注册成功
	 */
	// UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	bool RegisterTileMap(AActor *Character, EEnemyRace EnemyTypeID);

	/**
	 * 注销角色的TileMap
	 * @param Character 角色
	 * @param EnemyTypeID 敌人类型
	 */
	// UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	void UnRegisterTileMap(AActor *Character, EEnemyRace EnemyTypeID);

	/**
	 * 获取特定类型敌人的TileMap
	 * @param FindInThisMap 在哪个TileMap中查找
	 * @param EnemyTypeID 敌人类型
	 * @return TileMap数据
	 */
	// UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	TSharedPtr<FGridTileMap> GetTileMap(AXwPaperTileMapActor *FindInThisMap, EEnemyRace EnemyTypeID);

	/**
	 * 获取特定类型敌人的JumpPointMap
	 * @param FindInThisMap 在哪个TileMap中查找
	 * @param EnemyTypeID 敌人类型
	 * @return JumpPointMap数据，如果不存在则返回nullptr
	 */
	// UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	TSharedPtr<FJumpPointMap> GetJumpPointMap(AXwPaperTileMapActor *FindInThisMap, EEnemyRace EnemyTypeID);

	/**
	 * 获取当前世界的TileMapManager实例
	 * @return TileMapManager实例
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|TileMap")
	static AGridTileMapManager *GetManagerInCurrentWorld();

	/**
	 * 通知TileMap发生了变化
	 * @param TileMapActor 发生变化的TileMap
	 */
	void NotifyTileMapChanged(AXwPaperTileMapActor *TileMapActor);

	/**
	 * 重新生成所有依赖指定TileMap的导航数据
	 * @param TileMapActor 需要更新的TileMap
	 */
	void RebuildNavigationForTileMap(AXwPaperTileMapActor *TileMapActor);

private:
	/**
	 * 为角色创建默认的TileMap
	 * @param Character 角色
	 * @param EnemyTypeID 敌人类型
	 * @return 创建的TileMap
	 */
	TSharedPtr<FGridTileMap> GetDefaultMap(AActor *Character, EEnemyRace EnemyTypeID);

	/**
	 * 为角色创建JumpPointMap
	 * @param Character 角色
	 * @param BaseMap 基础TileMap
	 * @return 创建的JumpPointMap
	 */
	TSharedPtr<FJumpPointMap> CreateJumpPointMap(ACharacterBase *Character, const TSharedPtr<FGridTileMap> &BaseMap);

private:
	/** TileMap注册表 */
	TMap<AXwPaperTileMapActor *, TMap<EEnemyRace, TSharedPtr<FGridTileMap>>> TileMapRegistry;

	/** 当前世界的TileMapManager实例 */
	static AGridTileMapManager *InstanceInCurrentWorld;
};
