// Fill out your copyright notice in the Description page of Project Settings.


#include "GAS/GameAbility/GameplayAbilityBase.h"
#include "Character/CharacterBase.h"
#include "PaperZDAnimInstance.h"
#include "AnimSequences\PaperZDAnimSequence.h"

const ACharacterBase* UGameplayAbilityBase::GetOwningPaperZDCharacter()
{
	return Cast<ACharacterBase>(GetActorInfo().OwnerActor);
}

const UPaperZDAnimInstance* UGameplayAbilityBase::GetPaperZDAnimInstance()
{
	const ACharacterBase* Character = GetOwningPaperZDCharacter();
	if(Character == nullptr) return nullptr;
	return Character->GetAnimInstance();
}

const UPaperZDAnimSequence* UGameplayAbilityBase::GetPaperZDAnimSequence()
{
	const ACharacterBase* Character = GetOwningPaperZDCharacter();
	if (Character == nullptr) return AbilityAnimSQ;

	auto ConfiguredSQ = Character->GetConfiguredAnimSQ_ByAbilityTagName(AbilityTags.First().GetTagName());
	if (ConfiguredSQ != nullptr)
		return ConfiguredSQ;
	else
		return AbilityAnimSQ;
}

const FGameplayTagContainer* UGameplayAbilityBase::GetCooldownTags() const
{
	FGameplayTagContainer* MutableTags = const_cast<FGameplayTagContainer*>(&TempCooldownTags);
	MutableTags->Reset();
	// MutableTags writes to the TempCooldownTags on the CDO so clear it in case the ability cooldown tags change (moved to a different slot)
	const FGameplayTagContainer* ParentTags = Super::GetCooldownTags();
	if (ParentTags)
	{
		MutableTags->AppendTags(*ParentTags);
	}
	MutableTags->AppendTags(CooldownTags);
	return MutableTags;
}

void UGameplayAbilityBase::ApplyCooldown(const FGameplayAbilitySpecHandle Handle,
	const FGameplayAbilityActorInfo* ActorInfo,
	const FGameplayAbilityActivationInfo ActivationInfo) const
{
	// Reference: https://github.com/BillEliot/GASDocumentation_Chinese?tab=readme-ov-file#concepts-ge-mmc
	// goto 4.5.15https://github.com/BillEliot/GASDocumentation_Chinese?tab=readme-ov-file#4515-%E5%86%B7%E5%8D%B4cooldowngameplayeffect

	UGameplayEffect* CooldownGE = GetCooldownGameplayEffect();
	// When CoolDownGE's Magnitude Calculation Type is SetByCaller, uncomment follow
	// Ensure this GE asset has configure its Data Tag as "Data.Cooldown"
	//if (CooldownGE)
	//{
	//	FGameplayEffectSpecHandle SpecHandle =
	//		MakeOutgoingGameplayEffectSpec(CooldownGE->GetClass(), GetAbilityLevel());
	//	SpecHandle.Data.Get()->DynamicGrantedTags.AppendTags(CooldownTags);
	//	SpecHandle.Data.Get()->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.Cooldown")),
	//		CooldownDuration.GetValueAtLevel(GetAbilityLevel()));
	//	ApplyGameplayEffectSpecToOwner(Handle, ActorInfo, ActivationInfo, SpecHandle);
	//}

	// When CoolDownGE's Magnitude Calculation Type is Modifier Magnitude Calculation, uncomment follow
	// Ensure this GE asset use MMC_AbilityCoolDown or its subclass
	if (CooldownGE)
	{
		FGameplayEffectSpecHandle SpecHandle = MakeOutgoingGameplayEffectSpec(CooldownGE->GetClass(), GetAbilityLevel());
		SpecHandle.Data.Get()->DynamicGrantedTags.AppendTags(CooldownTags);
		ApplyGameplayEffectSpecToOwner(Handle, ActorInfo, ActivationInfo, SpecHandle);
	}

}

UGameplayEffect* UGameplayAbilityBase::GetCostGameplayEffect() const
{
	return Super::GetCostGameplayEffect();
}

void UGameplayAbilityBase::OnGiveAbility(const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilitySpec& Spec)
{
	Super::OnGiveAbility(ActorInfo, Spec);

	if (SubAbilities.Num() > 0)
	{
		auto AbilitySystemComp = ActorInfo->AbilitySystemComponent;
		for (auto AbilityClass : SubAbilities)
		{
			auto Handle = AbilitySystemComp->GiveAbility(FGameplayAbilitySpec(AbilityClass.GetDefaultObject(), GetAbilityLevel()));
			CachedSubAbilityHandles.Add(Handle);
		}
		AbilitySystemComp->InitAbilityActorInfo(ActorInfo->OwnerActor.Get(), ActorInfo->AvatarActor.Get());
	}
}

void UGameplayAbilityBase::OnRemoveAbility(const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilitySpec& Spec)
{
	if (CachedSubAbilityHandles.Num() > 0)
	{
		auto AbilitySystemComp = ActorInfo->AbilitySystemComponent;
		for (auto Hnadle : CachedSubAbilityHandles)
		{
			AbilitySystemComp->ClearAbility(Hnadle);
		}
		CachedSubAbilityHandles.Empty();
	}

	Super::OnRemoveAbility(ActorInfo, Spec);
}
