#include "Navigation/Components/DynamicPlatformComponent.h"
#include "Navigation/TileMap/XwPaperTileMapActor.h"
#include "Navigation/TileMap/GridTileMapManager.h"
#include "Navigation/Utilities/NavigationUtilities.h"
#include "Navigation/Core/XwNavTypes.h"
#include "Components/PrimitiveComponent.h"
#include "PaperSpriteComponent.h"

UDynamicPlatformComponent::UDynamicPlatformComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	bIsRegistered = false;
	CurrentPlatformType = ENavigationTileType::Ground;
}

void UDynamicPlatformComponent::BeginPlay()
{
	Super::BeginPlay();
	
	// 如果没有设置碰撞组件，尝试查找
	if (!CollisionComponent)
	{
		// 优先查找PaperSpriteComponent
		CollisionComponent = GetOwner()->FindComponentByClass<UPaperSpriteComponent>();
		
		// 如果没有找到，尝试查找其他碰撞组件
		if (!CollisionComponent)
		{
			CollisionComponent = GetOwner()->FindComponentByClass<UPrimitiveComponent>();
		}
	}
	
	// 查找所在的TileMapActor
	CachedTileMapActor = FindTileMapActor();
}

void UDynamicPlatformComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	// 注销动态平台
	if (bIsRegistered)
	{
		UnregisterAsPlatform();
	}
	
	Super::EndPlay(EndPlayReason);
}

bool UDynamicPlatformComponent::RegisterAsPlatform(ENavigationTileType PlatformType)
{
	// 如果已经注册，先注销
	if (bIsRegistered)
	{
		UnregisterAsPlatform();
	}
	
	// 查找所在的TileMapActor
	if (!CachedTileMapActor)
	{
		CachedTileMapActor = FindTileMapActor();
	}
	
	if (!CachedTileMapActor || !CollisionComponent)
	{
		return false;
	}
	
	// 计算占据的格子
	if (!CalculateOccupiedTiles(OccupiedTiles))
	{
		return false;
	}
	
	// 注册动态对象
	CurrentPlatformType = PlatformType;
	if (UNavigationUtilities::AddDynamicObjectToTileMap(GetOwner(), CachedTileMapActor, OccupiedTiles, CurrentPlatformType))
	{
		bIsRegistered = true;
		return true;
	}
	
	return false;
}

bool UDynamicPlatformComponent::UnregisterAsPlatform()
{
	if (!bIsRegistered || !CachedTileMapActor)
	{
		return false;
	}
	
	// 注销动态对象
	if (UNavigationUtilities::RemoveDynamicObjectFromTileMap(GetOwner(), CachedTileMapActor))
	{
		bIsRegistered = false;
		OccupiedTiles.Empty();
		return true;
	}
	
	return false;
}

void UDynamicPlatformComponent::SetCollisionComponent(UPrimitiveComponent* InCollisionComponent)
{
	// 如果已经注册，先注销
	if (bIsRegistered)
	{
		UnregisterAsPlatform();
	}
	
	CollisionComponent = InCollisionComponent;
}

AXwPaperTileMapActor* UDynamicPlatformComponent::FindTileMapActor() const
{
	// 查找TileMapManager
	AGridTileMapManager* Manager = AGridTileMapManager::GetManagerInCurrentWorld();
	if (!Manager)
	{
		return nullptr;
	}
	
	// 根据Actor位置查找TileMapActor
	return Manager->FindTileMapByLocation(GetOwner()->GetActorLocation());
}

bool UDynamicPlatformComponent::CalculateOccupiedTiles(TArray<FIntPoint>& OutOccupiedTiles) const
{
	if (!CollisionComponent || !CachedTileMapActor)
	{
		return false;
	}
	
	// 如果是PaperSpriteComponent，使用专门的方法
	UPaperSpriteComponent* SpriteComponent = Cast<UPaperSpriteComponent>(CollisionComponent);
	if (SpriteComponent)
	{
		return UNavigationUtilities::CalculateSpriteOccupiedTiles(SpriteComponent, CachedTileMapActor, OutOccupiedTiles);
	}
	
	// 否则使用通用方法
	return UNavigationUtilities::CalculateOccupiedTiles(CollisionComponent, CachedTileMapActor, OutOccupiedTiles);
} 