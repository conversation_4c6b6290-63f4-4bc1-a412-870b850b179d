// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Character/CharacterBase.h"
#include "Interaction/PlayerInterface.h"
#include "XwPlayer.generated.h"

class AController;
class UXwInventoryComponent;

/**
 *
 */
UCLASS()
class DREAMBOY_API AXwPlayer : public ACharacterBase, public IPlayerInterface
{
	GENERATED_BODY()
public:
	AXwPlayer();

	/*--------	Combat Interface Start	--------*/
	virtual int32 GetPlayerLevel_Implementation() const override;
	/*--------	Combat Interface End	--------*/

	/*--------	Combat Interface Start	--------*/
	virtual int32 FindLevelForXP_Implementation(int32 InXP) const override;
	virtual int32 GetXP_Implementation() const override;
	virtual void AddToXP_Implementation(int32 InXP) override;
	virtual void AddToPlayerLevel_Implementation(int32 InPlayerLevel) override;
	virtual void LevelUp_Implementation() override;
	virtual void SaveProgress_Implementation(const FName& CheckPointTag) override;
	/*--------	Combat Interface End	--------*/

protected:
	virtual void BeginPlay() override;
	virtual void PossessedBy(AController* NewController) override;

	/** 背包组件 */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Inventory")
	TObjectPtr<UXwInventoryComponent> InventoryComponent;

private:
	virtual bool LoadConfigAsset() override;
	virtual void InitReferences() override;
};
