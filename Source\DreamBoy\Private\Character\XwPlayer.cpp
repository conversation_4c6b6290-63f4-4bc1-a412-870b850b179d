// Fill out your copyright notice in the Description page of Project Settings.

#include "Character/XwPlayer.h"
#include "Player/XwPlayerController.h"
#include "Player/XwPlayerState.h"
#include "Components/CapsuleComponent.h"
#include "UI/HUD/XwHUD.h"
#include "Data/PlayerConfigAsset.h"
#include "Inventory/XwInventoryComponent.h"

AXwPlayer::AXwPlayer()
{
	Tags.Add("Player");
	GetCapsuleComponent()->SetCollisionProfileName(TEXT("XwPlayer"));

	// 创建背包组件
	InventoryComponent = CreateDefaultSubobject<UXwInventoryComponent>(TEXT("InventoryComponent"));
}

int32 AXwPlayer::GetPlayerLevel_Implementation() const
{
	AXwPlayerState* XwPlayerState = GetPlayerState<AXwPlayerState>();
	return XwPlayerState->GetPlayerLevel();
}

int32 AXwPlayer::FindLevelForXP_Implementation(int32 InXP) const
{
	return AXwPlayerState::GetLevelOfAbsoluteXP(InXP);
}

void AXwPlayer::AddToXP_Implementation(int32 InXP)
{
	AXwPlayerState* XwPlayerState = CastChecked<AXwPlayerState>(GetPlayerState());
	XwPlayerState->AddToXP(InXP);
}

int32 AXwPlayer::GetXP_Implementation() const
{
	AXwPlayerState* XwPlayerState = CastChecked<AXwPlayerState>(GetPlayerState());
	return XwPlayerState->GetXP();
}

void AXwPlayer::AddToPlayerLevel_Implementation(int32 InPlayerLevel)
{
	AXwPlayerState* XwPlayerState = CastChecked<AXwPlayerState>(GetPlayerState());
	XwPlayerState->AddToLevel(InPlayerLevel);
}

void AXwPlayer::LevelUp_Implementation()
{
	
}

void AXwPlayer::SaveProgress_Implementation(const FName& CheckPointTag)
{
	
}

void AXwPlayer::BeginPlay()
{
	Super::BeginPlay();
}

void AXwPlayer::PossessedBy(AController* NewController)
{
	Super::PossessedBy(NewController);

	InitReferences();
	InitAbilities();
	InitializeDefaultAttributes();

	// Init UI
	AXwPlayerState* XwPlayerState = GetPlayerState<AXwPlayerState>();
	if (auto* PlayerController = Cast<APlayerController>(GetController()))
	{
		if (AXwHUD* HUD = Cast<AXwHUD>(PlayerController->GetHUD()))
		{
			HUD->InitOverlay(PlayerController, XwPlayerState, AbilitySystemComponent, AttributeSet);
		}
	}
}

bool AXwPlayer::LoadConfigAsset()
{
	if (!Super::LoadConfigAsset())
		return false;

	auto PlayerConfigAsset = Cast<UPlayerConfigAsset>(ConfigAsset);
	if (!PlayerConfigAsset)
		return false;

	return true;
}

void AXwPlayer::InitReferences()
{
	AXwPlayerState* XwPlayerState = GetPlayerState<AXwPlayerState>();

	AbilitySystemComponent = XwPlayerState->GetAbilitySystemComponent();
	AttributeSet = XwPlayerState->GetAttributeSet();

	AbilitySystemComponent->InitAbilityActorInfo(XwPlayerState, this);
}
