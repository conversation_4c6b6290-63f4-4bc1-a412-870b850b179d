#include "Navigation/TileMap/TileMapNavigationBuilder.h"
#include "PaperTileMapComponent.h"
#include "PaperTileMap.h"
#include "PaperTileLayer.h"
#include "PaperTileSet.h"
#include "Navigation/TileMap/XwPaperTileMapActor.h"
#include "Navigation/Algorithms/JumpPointSearch/JumpPointTileMap.h"

bool UTileMapNavigationBuilder::BuildBaseNavigationGrid(FGridTileMap& OutGrid, UPaperTileMapComponent* TileMapComponent)
{
    if (!TileMapComponent || !TileMapComponent->TileMap)
    {
        UE_LOG(LogTemp, Error, TEXT("UTileMapNavigationBuilder::BuildBaseNavigationGrid: Invalid TileMap Component!"));
        return false;
    }

    const UPaperTileMap* TileMap = TileMapComponent->TileMap;
    const int32 MapWidth = TileMap->MapWidth;
    const int32 MapHeight = TileMap->MapHeight;
    const int32 TileSize = TileMap->TileWidth;

    // 提取导航数据
    TArray<TArray<ENavigationTileType>> GraphTiles;
    ExtractNavigationData(GraphTiles, TileMapComponent);
    
    // 处理地形数据
    // 这里是不是跟下面的Init重复了
    ProcessTerrainData(GraphTiles);
    
    // 初始化导航网格
    OutGrid.Init(GraphTiles, TileSize, TileMapComponent->GetTileCenterPosition(0, MapHeight - 1, 0, true));
    
    return true;
}

bool UTileMapNavigationBuilder::BuildJumpPointGrid(FJumpPointMap& OutGrid, UPaperTileMapComponent* TileMapComponent, const FPathFindingAthleticAttr& MovementCaps)
{
    FGridTileMap BaseGrid;
    if (!BuildBaseNavigationGrid(BaseGrid, TileMapComponent))
    {
        return false;
    }
    
    return BuildJumpPointGridFromBase(OutGrid, BaseGrid, MovementCaps);
}

bool UTileMapNavigationBuilder::BuildJumpPointGridFromBase(FJumpPointMap& OutGrid, const FGridTileMap& BaseGrid, const FPathFindingAthleticAttr& MovementCaps)
{
    // 这里是不得已而为之，之后需要重构，Init和InitWithParameter有些迷惑
    OutGrid.InitWithParameter(BaseGrid, MovementCaps);
    return true;
}

bool UTileMapNavigationBuilder::BuildNavigationGridFromActor(FGridTileMap& OutGrid, AXwPaperTileMapActor* TileMapActor)
{
    if (!TileMapActor)
    {
        UE_LOG(LogTemp, Error, TEXT("UTileMapNavigationBuilder::BuildNavigationGridFromActor: Invalid TileMap Actor!"));
        return false;
    }
    
    return BuildBaseNavigationGrid(OutGrid, TileMapActor->GetRenderComponent());
}

void UTileMapNavigationBuilder::ExtractNavigationData(TArray<TArray<ENavigationTileType>>& OutTiles, const UPaperTileMapComponent* TileMapComp)
{
    if (!TileMapComp || !TileMapComp->TileMap)
    {
        return;
    }

    const UPaperTileMap* TileMap = TileMapComp->TileMap;
    const int32 MapWidth = TileMap->MapWidth;
    const int32 MapHeight = TileMap->MapHeight;
    const int32 TileLayers = TileMap->TileLayers.Num();

    // 初始化输出数组
    OutTiles.SetNum(MapWidth);
    for (int X = 0; X < MapWidth; ++X)
    {
        OutTiles[X].SetNum(MapHeight);
        for (int Y = 0; Y < MapHeight; ++Y)
        {
            OutTiles[X][Y] = ENavigationTileType::Empty;
        }
    }

    // 遍历所有图层（假设第0层是地形层）
    for (int32 LayerIndex = 0; LayerIndex < TileLayers; ++LayerIndex)
    {
        const UPaperTileLayer* Layer = TileMap->TileLayers[LayerIndex];
        if (!Layer || !Layer->LayerName.EqualTo(FText::FromString("Terrain")))
        {
            continue; // 只处理地形层
        }

        // 原本Tile原点在左上角（跟DX的Tex同种坐标系），这里翻转一下Y，构建成原点在左下角
        for (int32 X = 0; X < MapWidth; ++X)
        {
            for (int32 Y = MapHeight - 1; Y >= 0; --Y)
            {
                const FPaperTileInfo TileInfo = Layer->GetCell(X, Y);
                if (TileInfo.IsValid() && TileInfo.TileSet)
                {
                    auto MetaData = TileInfo.TileSet->GetTileMetadata(TileInfo.GetTileIndex());
                    if (MetaData && MetaData->HasCollision())
                    {
                        OutTiles[X][MapHeight - 1 - Y] = ENavigationTileType::Obstacle;
                    }
                }
            }
        }
    }
}

void UTileMapNavigationBuilder::ProcessTerrainData(TArray<TArray<ENavigationTileType>>& InOutTiles)
{
    if (InOutTiles.IsEmpty())
    {
        return;
    }

    const int32 Width = InOutTiles.Num();
    const int32 Height = InOutTiles[0].Num();

    // 具有Block物理的格子正上方的空格子视为地面
    for (int32 X = 0; X < Width; ++X)
    {
        for (int32 Y = 1; Y < Height; ++Y)
        {
            if (InOutTiles[X][Y] == ENavigationTileType::Empty && 
                Y > 0 && (InOutTiles[X][Y - 1] == ENavigationTileType::Obstacle || InOutTiles[X][Y - 1] == ENavigationTileType::CrossablePlatform))
            {
                InOutTiles[X][Y] = ENavigationTileType::Ground;
            }
        }
    }
} 