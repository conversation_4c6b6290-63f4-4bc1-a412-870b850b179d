// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/UserWidget/XwUserWidget.h"
#include "Blueprint/WidgetTree.h"
#include "Components/SizeBox.h"

void UXwUserWidget::SetWidgetSize(float Width, float Height)
{
	WidgetWidth = Width;
	WidgetHeight = Height;

	USizeBox* RootWidget = Cast<USizeBox>(WidgetTree->RootWidget);
	if(RootWidget)
	{
		RootWidget->SetWidthOverride(WidgetWidth);
		RootWidget->SetHeightOverride(WidgetHeight);
	}
	K2_OnWidgetSizeSet(WidgetWidth, WidgetHeight);
}


void UXwUserWidget::SetWidgetController(UObject* InWidgetController)
{
	// Handle self
	WidgetController = InWidgetController;
	WidgetControllerSet();

	// Handle children
	TArray<UXwUserWidget*> AllWidgets;

	WidgetTree->ForEachWidget([&AllWidgets](UWidget* Widget) {
		UXwUserWidget* XwUserWidget = Cast<UXwUserWidget>(Widget);
		if(XwUserWidget) AllWidgets.Add(XwUserWidget);
		});

	for (auto XwUserWidget : AllWidgets)
	{
		XwUserWidget->WidgetController = InWidgetController;
		XwUserWidget->WidgetControllerSet();
	}
}
