// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "XwUserWidget.generated.h"

/**
 * Represent for UI MVC's V, and C is XwWidgetController, M is XwAbilitySystemComponent
 */
UCLASS()
class DREAMBOY_API UXwUserWidget : public UUserWidget
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void InitStyle();

	UFUNCTION(BlueprintCallable)
	void SetWidgetSize(float Width, float Height);
	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "OnWidgetSizeSet", ScriptName = "OnWidgetSizeSet"))
	void K2_OnWidgetSizeSet(float Width, float Height);

	UFUNCTION(BlueprintCallable)
	void SetWidgetController(UObject* InWidgetController);

	// Object to get GAS data and other, can be anything. Instance of UXwWidgetController, ACharacterBase, ....
	UPROPERTY(BlueprintReadOnly)
	TObjectPtr<UObject> WidgetController;

protected:
	UFUNCTION(BlueprintImplementableEvent)
	void WidgetControllerSet();

protected:
	UPROPERTY(BlueprintReadWrite)
	float WidgetWidth;
	UPROPERTY(BlueprintReadWrite)
	float WidgetHeight;
};
