#pragma once

#include "CoreMinimal.h"
#include "XwNavTypes.generated.h"

class ACharacterBase;

// 导航网格的基础类型定义
UENUM(BlueprintType)
enum class ENavigationTileType : uint8
{
    Empty       UMETA(DisplayName = "Empty"),
    Ground      UMETA(DisplayName = "Ground"),
    Obstacle    UMETA(DisplayName = "Obstacle"),
	CrossablePlatform UMETA(DisplayName = "CrossablePlatform"),
};

// 角色运动属性
USTRUCT(BlueprintType)
struct FPathFindingAthleticAttr
{
	GENERATED_BODY()

	// 网格大小
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	int32 TileSize = 32;

	// 角色碰撞体大小
	//UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	FIntVector2 BodySize;

	// 重力加速度
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	float Gravity = 980.0f;

	// 跳跃初速度
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	float JumpZVelocity;

	// 最大行走速度
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	float MaxWalkSpeed;

	// 最大跳跃持续时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	float JumpMaxHoldTime;

	static FPathFindingAthleticAttr GenerateFromCharacter(ACharacterBase* Character, int32 MapTileSize);
};


// 导航请求参数
USTRUCT(BlueprintType)
struct DREAMBOY_API FNavigationRequest
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
    FVector StartLocation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
    FVector TargetLocation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
    float AcceptanceRadius = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDebugInfo = false;
}; 