// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Abilities/GameplayAbility.h"
#include "GameplayAbilityBase.generated.h"

class ACharacterBase;
class UPaperZDAnimInstance;
class UPaperZDAnimSequence;

UENUM(BlueprintType)
enum class EGameplayAbilityType : uint8
{
	Trigger,
	Hold,
	Switch
};

/**
 * 
 */
UCLASS()
class DREAMBOY_API UGameplayAbilityBase : public UGameplayAbility
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable)
	const ACharacterBase* GetOwningPaperZDCharacter();
	UFUNCTION(BlueprintCallable)
	const UPaperZDAnimInstance* GetPaperZDAnimInstance();
	UFUNCTION(BlueprintCallable)
	const UPaperZDAnimSequence* GetPaperZDAnimSequence();
	
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Anim")
	EGameplayAbilityType AbilityType;

	UPROPERTY(BlueprintReadOnly, EditAnywhere, Category = "Anim")
	TObjectPtr<UPaperZDAnimSequence> AbilityAnimSQ;

	UPROPERTY(BlueprintReadOnly, EditAnywhere, Category = "Cooldowns")
	FScalableFloat CooldownDuration;

	UPROPERTY(BlueprintReadOnly, EditAnywhere, Category = "Cooldowns")
	FGameplayTagContainer CooldownTags;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Costs")
	FScalableFloat HealthCost;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Costs")
	FScalableFloat ManaCost;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Costs")
	FScalableFloat EnergyCost;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TArray<TSubclassOf<UGameplayAbility>> SubAbilities;
	TArray<FGameplayAbilitySpecHandle> CachedSubAbilityHandles;

	// Temp container that we will return the pointer to in GetCooldownTags().
	   // This will be a union of our CooldownTags and the Cooldown GE's Cooldown tags.
	UPROPERTY(Transient)
	FGameplayTagContainer TempCooldownTags;


public:
	// Return the union of our Cooldown Tags and any existing Cooldown GE's tags.
	virtual const FGameplayTagContainer* GetCooldownTags() const override;

	// Inject our Cooldown Tags and to add the SetByCaller to the cooldown GameplayEffectSpec.
	virtual void ApplyCooldown(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo) const override;

	virtual UGameplayEffect* GetCostGameplayEffect() const override;

	virtual void OnGiveAbility(const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilitySpec& Spec) override;
	virtual void OnRemoveAbility(const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilitySpec& Spec) override;
};
