#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "DynamicPlatformComponent.generated.h"

enum class ENavigationTileType : uint8;
class AXwPaperTileMapActor;
class UPrimitiveComponent;

/**
 * 动态平台组件
 * 用于将Actor注册为动态导航平台
 */
UCLASS(ClassGroup=(Navigation), meta=(BlueprintSpawnableComponent))
class DREAMBOY_API UDynamicPlatformComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	UDynamicPlatformComponent();

protected:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:	
	/**
	 * 注册为动态平台
	 * @param PlatformType 平台类型（导航格子类型）
	 * @return 是否注册成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Platform")
	bool RegisterAsPlatform(ENavigationTileType PlatformType);
	
	/**
	 * 注销动态平台
	 * @return 是否注销成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Platform")
	bool UnregisterAsPlatform();
	
	/**
	 * 设置碰撞组件
	 * @param InCollisionComponent 碰撞组件
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Platform")
	void SetCollisionComponent(UPrimitiveComponent* InCollisionComponent);

private:
	/**
	 * 查找所在的TileMapActor
	 * @return 所在的TileMapActor
	 */
	AXwPaperTileMapActor* FindTileMapActor() const;
	
	/**
	 * 计算占据的格子
	 * @param OutOccupiedTiles 输出占据的格子
	 * @return 是否计算成功
	 */
	bool CalculateOccupiedTiles(TArray<FIntPoint>& OutOccupiedTiles) const;

private:
	/** 是否已注册 */
	bool bIsRegistered;
	
	/** 平台类型 */
	ENavigationTileType CurrentPlatformType;
	
	/** 碰撞组件 */
	UPROPERTY()
	TObjectPtr<UPrimitiveComponent> CollisionComponent;
	
	/** 缓存的TileMapActor */
	UPROPERTY()
	TObjectPtr<AXwPaperTileMapActor> CachedTileMapActor;
	
	/** 占据的格子 */
	TArray<FIntPoint> OccupiedTiles;
}; 