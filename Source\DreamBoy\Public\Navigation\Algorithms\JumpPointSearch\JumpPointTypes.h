#pragma once

#include "CoreMinimal.h"
#include "Navigation/Core/XwNavTypes.h"
//#include "JumpPointTypes.generated.h"

// 导航连接类型
enum ENavLinkType
{
	MoveLink,
	FallLink,
	JumpLink
};

struct FNavLinkInfo
{
	FNavLinkInfo(FIntVector2 FromPoint, FIntVector2 ToPoint, ENavLinkType LinkType = ENavLinkType::MoveLink)
		: From(FromPoint), To(ToPoint), Type(LinkType)
	{
	}
	FNavLinkInfo& operator=(const FNavLinkInfo& Right)
	{
		From = Right.From;
		To = Right.To;
		Type = Right.Type;
	}

	inline bool operator==(const FNavLinkInfo& Compare) const {
		return	From == Compare.From &&
			To == Compare.To &&
			Type == Compare.Type;
	}

	FIntVector2 From;
	FIntVector2 To;

	ENavLinkType Type;

	friend uint32 GetTypeHash(const FNavLinkInfo& Link)
	{
		return HashCombineFast(HashCombineFast(GetTypeHash(Link.From), GetTypeHash(Link.To)), GetTypeHash(Link.Type));
	}
};

struct FNavPointInfo
{
	FNavPointInfo(FIntVector2 position = FIntVector2(0, 0))
	{
		Position = position;
	}

	FNavPointInfo& operator=(const FNavPointInfo& Right)
	{
		Position = Right.Position;
		IsLeftEdge = Right.IsLeftEdge;
		IsRightEdge = Right.IsRightEdge;
		IsLeftWall = Right.IsLeftWall;
		IsRightWall = Right.IsRightWall;
		CanStandOn = Right.CanStandOn;
		KeyPointLinks = Right.KeyPointLinks;
		return *this;
	}

	inline bool operator==(const FNavPointInfo& Compare) const {
		return Position == Compare.Position;
	}

	inline void AddLinkTo(const FNavPointInfo& To, ENavLinkType LinkType = ENavLinkType::MoveLink) {
		KeyPointLinks.Add(FNavLinkInfo(Position, To.Position, LinkType));
	}
	inline void AddLinkTo(const FIntVector2& To, ENavLinkType LinkType = ENavLinkType::MoveLink) {
		KeyPointLinks.Add(FNavLinkInfo(Position, To, LinkType));
	}
	const FNavLinkInfo* GetLinkTo(const FNavPointInfo& To) const;
	const FNavLinkInfo* GetLinkTo(const FIntVector2& To) const;

	// 位置
	FIntVector2 Position = FIntVector2(0, 0);

	// NavPoint类型
	bool IsLeftEdge = false;
	bool IsRightEdge = false;
	bool IsLeftWall = false;
	bool IsRightWall = false;
	bool IsFallTile = false;

	TSet<FNavLinkInfo> KeyPointLinks;
	bool CanStandOn = false;
	bool CanWalkToHorizontalLeftPoint = false;
	bool CanWalkToHorizontalRightPoint = false;

	friend uint32 GetTypeHash(const FNavPointInfo& Point)
	{
		return GetTypeHash(Point.Position);
	}
};
