#pragma once

#include "CoreMinimal.h"
#include "Navigation/Core/XwNavTypes.h"
//#include "XwNavTileMap.generated.h"


/**
 * 导航网格基类
 * 提供基础的网格操作和查询功能
 */
struct FGridTileMap
{
	FGridTileMap() {}
	virtual ~FGridTileMap() {}
	FGridTileMap& operator=(const FGridTileMap& InMap);

	virtual void Init(const TArray<TArray<ENavigationTileType>>& InTileMap, int32 TileSize, FVector WorldOrigin);
	virtual void InitTiles();
	virtual void RefreshCache();

	bool IsValidPos(int Horizon, int Vertical) const;
	ENavigationTileType GetTile(int Horizon, int Vertical) const;
	bool CanStandOn(int Horizon, int Vertical) const;
	bool IsObstacle(int Horizon, int Vertical) const;
	// 其实相当于A*导航算法中能用的点（角色能走的点）
	bool IsGround(int Horizon, int Vertical) const;
	virtual bool HitTest(FIntVector2 UpLeft, FIntVector2 DownRight, TArray<FIntVector2>* HitTiles = nullptr);

	FIntVector2			WorldToLocal(const FVector& WorldPos) const;
	FVector				LocalToWorld(const FIntVector2& LocalPos) const;
	bool				FindNearestGround(const FIntVector2& LocalPos, FIntVector2& OutResult) const;


	// 假设网格原点在左下角，笛卡尔坐标系
	FVector GridWorldOrigin = FVector();
	// 请确保Tile都是正方形格子
	int32 GridTileSize = 1;
	FIntVector2 MapSize = FIntVector2();
	TArray<TArray<ENavigationTileType>> TileMap;
	TArray<FIntVector2> CachedGround;
};
