// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"

/**
 * 
 */
struct DREAMBOY_API FXwGameplayTags
{
public:
	static const FXwGameplayTags& Get();

	static void InitializeNativeGameplayTags();

	/*
	* Vital Attributes
	*/
	FGameplayTag Attributes_Health;
	FGameplayTag Attributes_Mana;
	FGameplayTag Attributes_Energy;

	/*
	* Primary Attributes
	*/
	FGameplayTag Attributes_Primary_Strength;
	FGameplayTag Attributes_Primary_Constitution;
	FGameplayTag Attributes_Primary_Agility;
	FGameplayTag Attributes_Primary_Vitality;

	/*
	* Secondary Attributes
	*/
	FGameplayTag Attributes_Secondary_MaxHealth;
	FGameplayTag Attributes_Secondary_MaxMana;
	FGameplayTag Attributes_Secondary_MaxEnergy;

	FGameplayTag Attributes_Secondary_Attack;
	FGameplayTag Attributes_Secondary_Defense;
	FGameplayTag Attributes_Secondary_CriticalHitChance;
	FGameplayTag Attributes_Secondary_CriticalHitDamage;
	FGameplayTag Attributes_Secondary_CriticalHitResistance;
	FGameplayTag Attributes_Secondary_ImmuneRate;
	FGameplayTag Attributes_Secondary_AttackSpeed;
	FGameplayTag Attributes_Secondary_MoveSpeed;

	/*
	* Meta Attributes
	*/
	FGameplayTag Attributes_Meta_IncomingXP;

	/*
	* InputTag
	*/
	/*FGameplayTag InputButton_Left;
	FGameplayTag InputButton_Right;
	FGameplayTag InputButton_Up;
	FGameplayTag InputButton_Down;
	FGameplayTag InputButton_;
	FGameplayTag InputButton_;
	FGameplayTag InputButton_;*/

	/* DamageTag */
	FGameplayTag Damage;


	FGameplayTag Effect_HitReact;


private:
	static FXwGameplayTags XwGameplayTags_StaticInstance;

	bool IsInit = false;
};
