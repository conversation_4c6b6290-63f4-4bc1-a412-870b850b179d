// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "UI/WidgetController/CharacterBuildWidgetController.h"
#include "XwUIBlueprintLibrary.generated.h"

class UXwUserWidget;
class AXwHUD;

/**
 * UI相关的蓝图函数库
 * 提供便捷的UI创建和管理功能
 */
UCLASS()
class DREAMBOY_API UXwUIBlueprintLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/**
	 * 创建并显示角色Build界面
	 * @param WorldContextObject 世界上下文对象
	 * @param BuildWidgetClass Build界面的Widget类
	 * @return 创建的Build界面Widget
	 */
	UFUNCTION(BlueprintCallable, Category = "XwUI|CharacterBuild", meta = (DefaultToSelf = "WorldContextObject"))
	static UXwUserWidget* ShowCharacterBuildWidget(const UObject* WorldContextObject, TSubclassOf<UXwUserWidget> BuildWidgetClass);

	/**
	 * 隐藏角色Build界面
	 * @param WorldContextObject 世界上下文对象
	 */
	UFUNCTION(BlueprintCallable, Category = "XwUI|CharacterBuild", meta = (DefaultToSelf = "WorldContextObject"))
	static void HideCharacterBuildWidget(const UObject* WorldContextObject);

	/**
	 * 获取当前的Build界面Widget
	 * @param WorldContextObject 世界上下文对象
	 * @return 当前的Build界面Widget，如果不存在则返回nullptr
	 */
	UFUNCTION(BlueprintPure, Category = "XwUI|CharacterBuild", meta = (DefaultToSelf = "WorldContextObject"))
	static UXwUserWidget* GetCharacterBuildWidget(const UObject* WorldContextObject);

	/**
	 * 检查Build界面是否正在显示
	 * @param WorldContextObject 世界上下文对象
	 * @return 如果Build界面正在显示则返回true
	 */
	UFUNCTION(BlueprintPure, Category = "XwUI|CharacterBuild", meta = (DefaultToSelf = "WorldContextObject"))
	static bool IsCharacterBuildWidgetVisible(const UObject* WorldContextObject);

	/**
	 * 切换Build界面的显示状态
	 * @param WorldContextObject 世界上下文对象
	 * @param BuildWidgetClass Build界面的Widget类
	 */
	UFUNCTION(BlueprintCallable, Category = "XwUI|CharacterBuild", meta = (DefaultToSelf = "WorldContextObject"))
	static void ToggleCharacterBuildWidget(const UObject* WorldContextObject, TSubclassOf<UXwUserWidget> BuildWidgetClass);

	/**
	 * 为Build界面设置输入模式
	 * @param WorldContextObject 世界上下文对象
	 * @param bUIOnly 是否只接受UI输入
	 */
	UFUNCTION(BlueprintCallable, Category = "XwUI|Input", meta = (DefaultToSelf = "WorldContextObject"))
	static void SetBuildWidgetInputMode(const UObject* WorldContextObject, bool bUIOnly = true);

	/**
	 * 恢复游戏输入模式
	 * @param WorldContextObject 世界上下文对象
	 */
	UFUNCTION(BlueprintCallable, Category = "XwUI|Input", meta = (DefaultToSelf = "WorldContextObject"))
	static void RestoreGameInputMode(const UObject* WorldContextObject);

	/**
	 * 创建模拟的Build物品数据（用于测试）
	 * @return 模拟的物品数据数组
	 */
	UFUNCTION(BlueprintCallable, Category = "XwUI|Testing")
	static TArray<FBuildItemInfo> CreateMockBuildItems();

	/**
	 * 获取槽位类型的显示名称
	 * @param SlotType 槽位类型
	 * @return 槽位的显示名称
	 */
	UFUNCTION(BlueprintPure, Category = "XwUI|Utility")
	static FText GetSlotTypeDisplayName(EBuildSlotType SlotType);

	/**
	 * 获取槽位类型的快捷键提示
	 * @param SlotType 槽位类型
	 * @return 快捷键提示文本
	 */
	UFUNCTION(BlueprintPure, Category = "XwUI|Utility")
	static FText GetSlotTypeKeyHint(EBuildSlotType SlotType);

	/**
	 * 检查物品是否可以装备到指定槽位
	 * @param ItemInfo 物品信息
	 * @param SlotType 目标槽位类型
	 * @return 如果可以装备则返回true
	 */
	UFUNCTION(BlueprintPure, Category = "XwUI|Utility")
	static bool CanEquipItemToSlot(const FBuildItemInfo& ItemInfo, EBuildSlotType SlotType);

private:
	// 存储当前显示的Build界面Widget的静态引用
	static TWeakObjectPtr<UXwUserWidget> CurrentBuildWidget;
};
