# DreamBoy 项目 clang-format 配置
# 基于 Unreal Engine 代码风格，针对指针声明进行调整

BasedOnStyle: Microsoft

# 指针和引用对齐 - 关键设置
PointerAlignment: Left
ReferenceAlignment: Left

# 基本格式设置
IndentWidth: 4
TabWidth: 4
UseTab: ForIndentation
ColumnLimit: 120

# 大括号风格
BreakBeforeBraces: Allman
Cpp11BracedListStyle: true

# 函数和控制语句
AllowShortFunctionsOnASingleLine: Empty
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false

# 空格设置
SpaceAfterCStyleCast: false
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesInParentheses: false
SpacesInSquareBrackets: false

# 对齐设置
AlignConsecutiveDeclarations: false
AlignConsecutiveAssignments: false
AlignTrailingComments: true

# 换行设置
AllowAllParametersOfDeclarationOnNextLine: true
BinPackParameters: false
BinPackArguments: false

# 访问修饰符
AccessModifierOffset: -4

# 预处理器
IndentPPDirectives: None

# 注释
ReflowComments: true

# 排序
SortIncludes: false
SortUsingDeclarations: false

# 其他设置
FixNamespaceComments: true
CompactNamespaces: false
