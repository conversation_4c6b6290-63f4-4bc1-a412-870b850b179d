// Fill out your copyright notice in the Description page of Project Settings.


#include "XwGameplayTags.h"
#include "GameplayTagsManager.h"

FXwGameplayTags FXwGameplayTags::XwGameplayTags_StaticInstance;


const FXwGameplayTags& FXwGameplayTags::Get()
{
	if (!XwGameplayTags_StaticInstance.IsInit)
	{
		InitializeNativeGameplayTags();
	}
	return XwGameplayTags_StaticInstance;
}

void FXwGameplayTags::InitializeNativeGameplayTags()
{
	// Fuck HotReload
	auto AddTagIfNotExits = [](FName TagName, FString Comment) {
		auto& Manager = UGameplayTagsManager::Get();
		auto Tag = FGameplayTag::RequestGameplayTag(TagName, false);
		if(Tag.IsValid())
			return Tag;
		else
			return Manager.AddNativeGameplayTag(TagName, Comment);
		};

	/*
	* Vital Attributes
	*/
	XwGameplayTags_StaticInstance.Attributes_Health = AddTagIfNotExits(
		FName("Attributes.Health"),
		FString("Health points"));

	XwGameplayTags_StaticInstance.Attributes_Mana = AddTagIfNotExits(
		FName("Attributes.Mana"),
		FString("Points to cast some spell"));

	XwGameplayTags_StaticInstance.Attributes_Energy = AddTagIfNotExits(
		FName("Attributes.Energy"),
		FString("Points to cast some ability"));

	/*
	 * Primary Attributes
	 */
	XwGameplayTags_StaticInstance.Attributes_Primary_Strength = AddTagIfNotExits(
		FName("Attributes.Primary.Strength"),
		FString("Increases physical damage"));

	XwGameplayTags_StaticInstance.Attributes_Primary_Constitution = AddTagIfNotExits(
		FName("Attributes.Primary.Constitution"),
		FString("Increases magical damage"));

	XwGameplayTags_StaticInstance.Attributes_Primary_Agility = AddTagIfNotExits(
		FName("Attributes.Primary.Agility"),
		FString("Increases Armor and Armor Penetration"));

	XwGameplayTags_StaticInstance.Attributes_Primary_Vitality = AddTagIfNotExits(
		FName("Attributes.Primary.Vitality"),
		FString("Increases maximum of Health, Mana, Energy"));

	/*
	* Secondary Attributes
	*/
	XwGameplayTags_StaticInstance.Attributes_Secondary_MaxHealth = AddTagIfNotExits(
		FName("Attributes.Secondary.MaxHealth"),
		FString("Maximum of Health"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_MaxMana = AddTagIfNotExits(
		FName("Attributes.Secondary.MaxMana"),
		FString("Maximum of Mana"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_MaxEnergy = AddTagIfNotExits(
		FName("Attributes.Secondary.MaxEnergy"),
		FString("Maximum of Energy"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_Attack = AddTagIfNotExits(
		FName("Attributes.Secondary.Attack"),
		FString("Attack"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_Defense = AddTagIfNotExits(
		FName("Attributes.Secondary.Defense"),
		FString("Defense"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_CriticalHitChance = AddTagIfNotExits(
		FName("Attributes.Secondary.CriticalHitChance"),
		FString("Chance of critical hit"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_CriticalHitDamage = AddTagIfNotExits(
		FName("Attributes.Secondary.CriticalHitDamage"),
		FString("Increases damage when critical hit"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_CriticalHitResistance = AddTagIfNotExits(
		FName("Attributes.Secondary.CriticalHitResistance"),
		FString("Decreases the chance be critical hit"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_ImmuneRate = AddTagIfNotExits(
		FName("Attributes.Secondary.ImmuneRate"),
		FString("Immune percent of damage"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_AttackSpeed = AddTagIfNotExits(
		FName("Attributes.Secondary.AttackSpeed"),
		FString("AttackSpeed"));

	XwGameplayTags_StaticInstance.Attributes_Secondary_MoveSpeed = AddTagIfNotExits(
		FName("Attributes.Secondary.MoveSpeed"),
		FString("MoveSpeed"));

	/*
	* Meta Attributes
	*/
	XwGameplayTags_StaticInstance.Attributes_Meta_IncomingXP = AddTagIfNotExits(
		FName("Attributes.Meta.IncomingXP"),
		FString("Incoming XP Meta Attributes"));

	XwGameplayTags_StaticInstance.Damage = AddTagIfNotExits(
		FName("Damage"),
		FString("Damage")
	);

	XwGameplayTags_StaticInstance.Effect_HitReact = AddTagIfNotExits(
		FName("Effects.HitReact"),
		FString("Tag granted when Hit Reacting")
	);


	XwGameplayTags_StaticInstance.IsInit = true;
}
