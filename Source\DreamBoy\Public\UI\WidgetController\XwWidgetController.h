// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "XwWidgetController.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerStatChangedSignature, int32, NewValue);

class APlayerController;
class AXwPlayerController;
class APlayerState;
class AXwPlayerState;
class UAbilitySystemComponent;
class UXwAbilitySystemComponent;
class UAttributeSet;
class UAttributeSetBase;


USTRUCT(BlueprintType)
struct FWidgetControllerParams
{
	GENERATED_BODY()

public:
	FWidgetControllerParams() {}

	FWidgetControllerParams(APlayerController* pc, APlayerState* ps, UAbilitySystemComponent* asc, UAttributeSet* as)
		: PC(pc), PS(ps), ASC(asc), AS(as)
	{
	}


	UPROPERTY()
	TObjectPtr<APlayerController> PC;

	UPROPERTY()
	TObjectPtr<APlayerState> PS;

	UPROPERTY()
	TObjectPtr<UAbilitySystemComponent> ASC;

	UPROPERTY()
	TObjectPtr<UAttributeSet> AS;
};

/**
 * Represent for UI MVC's C, and V is XwUserWiget, M is XwAbilitySystemComponent
 */
UCLASS(BlueprintType)
class DREAMBOY_API UXwWidgetController : public UObject
{
	GENERATED_BODY()
	
public:
	UFUNCTION(BlueprintCallable)
	void SetWidgetControllerParams(const FWidgetControllerParams& WCParams);

	UFUNCTION(BlueprintCallable)
	virtual void BroadcastInitialValue() {};

	virtual void BindCallbacksToDependencies() {};

protected:

	UPROPERTY(BlueprintReadOnly, Category = "WidgetController")
	TObjectPtr<APlayerController> PlayerController;

	UPROPERTY(BlueprintReadOnly, Category = "WidgetController")
	TObjectPtr<APlayerState> PlayerState;

	UPROPERTY(BlueprintReadOnly, Category = "WidgetController")
	TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

	UPROPERTY(BlueprintReadOnly, Category = "WidgetController")
	TObjectPtr<UAttributeSet> AttributeSet;

	// For convenience
	TObjectPtr<AXwPlayerController> XwPlayerController;
	UFUNCTION(BlueprintPure)
	AXwPlayerController* GetXwPC();

	TObjectPtr<AXwPlayerState> XwPlayerState;
	UFUNCTION(BlueprintPure)
	AXwPlayerState* GetXwPS();

	TObjectPtr<UXwAbilitySystemComponent> XwAbilitySystemComponent;
	UFUNCTION(BlueprintPure)
	UXwAbilitySystemComponent* GetXwASC();

	TObjectPtr<UAttributeSetBase> XwAttributeSet;
	UFUNCTION(BlueprintPure)
	UAttributeSetBase* GetXwAS();
};
