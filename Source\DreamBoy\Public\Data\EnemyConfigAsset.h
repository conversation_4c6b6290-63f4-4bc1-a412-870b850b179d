// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Data/XwCharacterConfigAsset.h"
#include "Character/CharacterDefines.h"
#include "EnemyConfigAsset.generated.h"

class UUserWidget;
class AController;
class UBehaviorTree;


/**
 * 
 */
UCLASS(BlueprintType)
class DREAMBOY_API UEnemyConfigAsset : public UXwCharacterConfigAsset
{
	GENERATED_BODY()
	
public:
	UPROPERTY(EditDefaultsOnly)
	EEnemyRace Race;
	UPROPERTY(EditDefaultsOnly)
	int ObtainXP;
	
	UPROPERTY(EditDefaultsOnly)
	TSubclassOf<UUserWidget> UIWidgetClass;

	UPROPERTY(EditDefaultsOnly)
	TSubclassOf<AController> AIControllerClass;
	UPROPERTY(EditDefaultsOnly)
	TObjectPtr<UBehaviorTree> BehaviorTree;
};
