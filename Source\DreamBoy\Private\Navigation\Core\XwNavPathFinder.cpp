#include "Navigation/Core/XwNavPathFinder.h"
#include "PaperTileLayer.h"
#include "PaperTileSet.h"
#include "PaperTileMap.h"
#include "PaperTileMapComponent.h"

void PathFindingUtils::BuildPathFindingMap(FGridTileMap& PathFindingMap, const UPaperTileMapComponent* TileMapComp)
{
	if (!TileMapComp || !TileMapComp->TileMap)
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid TileMap Component!"));
		return;
	}

	const UPaperTileMap* TileMap = TileMapComp->TileMap;
	const int32 MapWidth = TileMap->MapWidth;
	const int32 MapHeight = TileMap->MapHeight;
	const int32 TileSize = TileMap->TileWidth;
	const int32 TileLayers = TileMap->TileLayers.Num();

	TArray<TArray<ENavigationTileType>> GraphTiles;
	GraphTiles.SetNum(MapWidth);
	for (int X = 0; X < MapWidth; ++X)
	{
		GraphTiles[X].SetNum(MapHeight);
	}

	// 1. 读取所有带物理的Tile格子
	// 遍历所有图层（假设第0层是地形层）
	for (int32 LayerIndex = 0; LayerIndex < TileLayers; ++LayerIndex)
	{
		const UPaperTileLayer& Layer = *(TileMap->TileLayers[LayerIndex]);
		if (!Layer.LayerName.EqualTo(FText::FromString("Terrain"))) continue; // 只处理地形层

		// 原本Tile原点在左上角（跟DX的Tex同种坐标系），这里翻转一下Y，构建成原点在左下角
		for (int32 X = 0; X < MapWidth; ++X)
		{
			for (int32 Y = MapHeight - 1; Y >= 0; --Y)
			{
				ENavigationTileType TileType = ENavigationTileType::Empty;
				const FPaperTileInfo TileInfo = Layer.GetCell(X, Y);
				if (TileInfo.IsValid())
				{
					if (TileInfo.TileSet)
					{
						auto MetaData = TileInfo.TileSet->GetTileMetadata(TileInfo.GetTileIndex());
						if (MetaData && MetaData->HasCollision())
							TileType = ENavigationTileType::Obstacle;
					}
				}

				GraphTiles[X][MapHeight - 1 - Y] = TileType;
			}
		}
	}

	PathFindingMap.Init(GraphTiles, TileSize, TileMapComp->GetTileCenterPosition(0, MapHeight - 1, 0, true));
}


void UAStarPathFindingBase::OnInitFinished()
{
	if (ShowDebugInfo) {
		DrawDebug();
	}
}

int32 UAStarPathFindingBase::Distance(FIntVector2 From, FIntVector2 To) const
{
	return FMath::Abs(From.X - To.X) + FMath::Abs(From.Y - To.Y);
}
