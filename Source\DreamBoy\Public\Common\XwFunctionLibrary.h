// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "XwFunctionLibrary.generated.h"

class AActor;

/**
 * 
 */
UCLASS()
class DREAMBOY_API UXwFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintPure, Category = "RPGAbilitySystemLibrary")
	static float GetXpAttenuationOfLevel(const UObject* WorldContextObject, int PlayerLevl, int EnemyLevel);
	UFUNCTION(BlueprintPure, Category = "RPGAbilitySystemLibrary")
	static int GetXPCanObatain(const UObject* WorldContextObject, AActor* Player, AActor* Enemy);
};
