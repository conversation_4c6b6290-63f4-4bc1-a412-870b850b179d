// Fill out your copyright notice in the Description page of Project Settings.


#include "GAS/MMC/Attribute/MMC_Attribute_ImmuneRate.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "Interaction/CombatInterface.h"
#include "Character/CharacterBase.h"
#include "Data/XwCharacterConfigAsset.h"

UMMC_Attribute_ImmuneRate::UMMC_Attribute_ImmuneRate()
{
}

float UMMC_Attribute_ImmuneRate::CalculateBaseMagnitude_Implementation(const FGameplayEffectSpec& Spec) const
{
	float BasicValue = 0.0;
	if (ACharacterBase* Character = Cast<ACharacterBase>(Spec.GetContext().GetSourceObject()))
	{
		if (UXwCharacterConfigAsset* ConfigAsset = Character->GetConfigAsset())
		{
			BasicValue = ConfigAsset->BasicValue_MaxHealth;
		}
	}	

	return BasicValue;
}
