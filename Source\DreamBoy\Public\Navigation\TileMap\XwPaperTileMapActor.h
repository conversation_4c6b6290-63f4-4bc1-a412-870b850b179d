// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "PaperTileMapActor.h"
#include "XwPaperTileMapActor.generated.h"

enum class ENavigationTileType : uint8;
struct FGridTileMap;
class AActor;

/**
 * 动态对象占据的格子信息
 */
USTRUCT()
struct FDynamicTileObject
{
	GENERATED_BODY()

	/** 关联的Actor */
	TWeakObjectPtr<AActor> OwnerActor;

	/** 占据的格子坐标 */
	TArray<FIntPoint> OccupiedTiles;

	/** 占据的格子类型 */
	TMap<FIntPoint, ENavigationTileType> TileTypes;

	FDynamicTileObject() {}

	FDynamicTileObject(AActor* InActor)
		: OwnerActor(InActor)
	{
	}
};

/**
 * 扩展的PaperTileMapActor，支持导航功能
 */
UCLASS()
class DREAMBOY_API AXwPaperTileMapActor : public APaperTileMapActor
{
	GENERATED_BODY()

public:	
	virtual void BeginPlay() override;
	virtual void BeginDestroy() override;

	/**
	 * 添加动态对象
	 * @param Actor 动态对象
	 * @param OccupiedTiles 占据的格子坐标
	 * @param TileType 占据的格子类型
	 * @return 是否添加成功
	 */
	bool AddDynamicObject(AActor* Actor, const TArray<FIntPoint>& OccupiedTiles, ENavigationTileType TileType);

	/**
	 * 移除动态对象
	 * @param Actor 动态对象
	 * @return 是否移除成功
	 */
	bool RemoveDynamicObject(AActor* Actor);

	/**
	 * 获取TileMap数据
	 * 返回的是基础TileMap和动态对象合并结果
	 * @return 合并后的TileMap数据
	 */
	TSharedPtr<FGridTileMap> GetTileMapData();

	/**
	 * 获取基础TileMap数据
	 * @return 基础TileMap数据
	 */
	TSharedPtr<FGridTileMap> GetBaseTileMapData() const { return GridTileMap; }

	/**
	 * 清除所有动态对象
	 */
	void ClearDynamicObjects();

	/**
	 * 判断指定位置是否被动态对象占据
	 * @param Position 位置
	 * @return 是否被占据
	 */
	bool IsTileOccupiedByDynamicObject(const FIntPoint& Position) const;

	/**
	 * 获取指定位置的动态对象
	 * @param Position 位置
	 * @return 动态对象，如果没有则返回nullptr
	 */
	AActor* GetDynamicObjectAtTile(const FIntPoint& Position) const;

private:
	/**
	 * 合并基础TileMap和动态对象
	 * @return 合并后的TileMap
	 */
	TSharedPtr<FGridTileMap> MergeTileMaps() const;

	/**
	 * 更新合并的TileMap
	 */
	void UpdateMergedTileMap();

private:
	/** 基础TileMap数据 */
	TSharedPtr<FGridTileMap> GridTileMap;
	
	/** 动态对象列表 */
	TMap<TWeakObjectPtr<AActor>, FDynamicTileObject> DynamicObjects;

	/** 格子到动态对象的映射，用于快速查询 */
	TMap<FIntPoint, TWeakObjectPtr<AActor>> TileToObjectMap;

	/** 缓存的合并TileMap数据 */
	TSharedPtr<FGridTileMap> CachedMergedTileMap;

	/** 是否需要重新构建合并地图 */
	bool bNeedRebuildMergedMap;
};
