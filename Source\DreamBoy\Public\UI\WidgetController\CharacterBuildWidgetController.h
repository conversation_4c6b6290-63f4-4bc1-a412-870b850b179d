// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UI/WidgetController/XwWidgetController.h"
#include "GameplayTagContainer.h"
#include "Inventory/InventoryTypes.h"
#include "CharacterBuildWidgetController.generated.h"

// 前向声明
class UTexture2D;
class UObject;
class UInventoryConfigDB;
class UXwInventoryComponent;

// 槽位类型枚举
UENUM(BlueprintType)
enum class EBuildSlotType : uint8
{
	None UMETA(DisplayName = "None"),
	Weapon UMETA(DisplayName = "Weapon"),
	Armor UMETA(DisplayName = "Armor"),
	Accessory UMETA(DisplayName = "Accessory"),
	Skill1 UMETA(DisplayName = "Skill1"),
	Skill2 UMETA(DisplayName = "Skill2"),
	Item UMETA(DisplayName = "Item"),
	Buff UMETA(DisplayName = "Buff")
};

// 物品信息结构体 - 简化版本，只保留槽位类型和物品ID
// 其他信息通过ItemID从背包系统的ConfigDB中获取
USTRUCT(BlueprintType)
struct FBuildItemInfo
{
	GENERATED_BODY()

	/** 槽位类型 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EBuildSlotType SlotType;

	/** 物品ID，对应背包系统中的ConfigID */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int32 ItemID;

	FBuildItemInfo()
	{
		SlotType = EBuildSlotType::None;
		ItemID = -1;
	}

	FBuildItemInfo(EBuildSlotType InSlotType, int32 InItemID) : SlotType(InSlotType), ItemID(InItemID) {}

	/** 检查是否为有效的物品信息 */
	bool IsValid() const
	{
		return ItemID > 0 && SlotType != EBuildSlotType::None;
	}

	/** 检查是否为空槽位 */
	bool IsEmpty() const
	{
		return ItemID <= 0;
	}
};

// 当前Build配置结构体
USTRUCT(BlueprintType)
struct FCurrentBuildConfig
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly)
	TMap<EBuildSlotType, FBuildItemInfo> EquippedItems;

	FCurrentBuildConfig()
	{
		// 初始化所有槽位为空
		EquippedItems.Add(EBuildSlotType::Weapon, FBuildItemInfo());
		EquippedItems.Add(EBuildSlotType::Armor, FBuildItemInfo());
		EquippedItems.Add(EBuildSlotType::Accessory, FBuildItemInfo());
		EquippedItems.Add(EBuildSlotType::Skill1, FBuildItemInfo());
		EquippedItems.Add(EBuildSlotType::Skill2, FBuildItemInfo());
		EquippedItems.Add(EBuildSlotType::Item, FBuildItemInfo());
		EquippedItems.Add(EBuildSlotType::Buff, FBuildItemInfo());
	}
};

// 委托声明
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSlotSelectedSignature, EBuildSlotType, SlotType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnItemSelectedSignature, const FBuildItemInfo&, ItemInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnItemEquippedSignature,
	EBuildSlotType, SlotType, const FBuildItemInfo&, ItemInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAvailableItemsUpdatedSignature, const TArray<FBuildItemInfo>&, AvailableItems);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBuildConfigUpdatedSignature, const FCurrentBuildConfig&, BuildConfig);

/**
 * 角色Build配置界面控制器
 * 管理装备、技能、道具、Buff的配置和显示
 */
UCLASS(BlueprintType, Blueprintable)
class DREAMBOY_API UCharacterBuildWidgetController : public UXwWidgetController
{
	GENERATED_BODY()

public:
	virtual void BroadcastInitialValue() override;
	virtual void BindCallbacksToDependencies() override;

	// 槽位选择
	UFUNCTION(BlueprintCallable)
	void SelectSlot(EBuildSlotType SlotType);

	// 物品选择和装备
	UFUNCTION(BlueprintCallable)
	void FocusItem(const FBuildItemInfo& ItemInfo);

	UFUNCTION(BlueprintCallable)
	void SelectItem(const FBuildItemInfo& ItemInfo);

	UFUNCTION(BlueprintCallable)
	void EquipItem(EBuildSlotType SlotType, const FBuildItemInfo& ItemInfo);

	// 获取当前Build配置
	UFUNCTION(BlueprintPure)
	FCurrentBuildConfig GetCurrentBuildConfig() const {	return CurrentBuildConfig; }

	// 获取指定槽位的可用物品列表
	UFUNCTION(BlueprintCallable)
	TArray<FBuildItemInfo> GetAvailableItemsForSlot(EBuildSlotType SlotType);

	// 获取当前选中的槽位
	UFUNCTION(BlueprintPure)
	EBuildSlotType GetCurrentSelectedSlot() const { return CurrentSelectedSlot;	}

	// 获取当前选中的物品
	UFUNCTION(BlueprintPure)
	FBuildItemInfo GetCurrentSelectedItem() const
	{
		return CurrentSelectedItem;
	}

	// ========== 背包系统集成接口 ==========

	// 从背包系统获取物品的显示名称
	UFUNCTION(BlueprintPure, Category = "Build|Inventory")
	FText GetItemDisplayName(int32 ItemID) const;

	// 从背包系统获取物品的描述
	UFUNCTION(BlueprintPure, Category = "Build|Inventory")
	FText GetItemDescription(int32 ItemID) const;

	// 从背包系统获取物品的图标
	UFUNCTION(BlueprintPure, Category = "Build|Inventory")
	UTexture2D* GetItemIcon(int32 ItemID) const;

	// 从背包系统获取物品的稀有度
	UFUNCTION(BlueprintPure, Category = "Build|Inventory")
	EEntityRarity GetItemRarity(int32 ItemID) const;

	// 检查玩家是否拥有指定物品
	UFUNCTION(BlueprintPure, Category = "Build|Inventory")
	bool HasItem(int32 ItemID) const;

	// 委托
	UPROPERTY(BlueprintAssignable, Category = "Build|Events")
	FOnSlotSelectedSignature OnSlotSelected;

	UPROPERTY(BlueprintAssignable, Category = "Build|Events")
	FOnItemSelectedSignature OnItemSelected;

	UPROPERTY(BlueprintAssignable, Category = "Build|Events")
	FOnItemSelectedSignature OnItemFocused;

	UPROPERTY(BlueprintAssignable, Category = "Build|Events")
	FOnItemEquippedSignature OnItemEquipped;

	UPROPERTY(BlueprintAssignable, Category = "Build|Events")
	FOnAvailableItemsUpdatedSignature OnAvailableItemsUpdated;

	UPROPERTY(BlueprintAssignable, Category = "Build|Events")
	FOnBuildConfigUpdatedSignature OnBuildConfigUpdated;

	// 属性相关委托（继承自基类的属性变化）
	UPROPERTY(BlueprintAssignable, Category = "GAS|Attributes")
	FOnPlayerStatChangedSignature OnPlayerLevelChanged;

	UPROPERTY(BlueprintAssignable, Category = "GAS|Attributes")
	FOnPlayerStatChangedSignature OnPlayerExpChanged;

private:
	// 当前Build配置
	UPROPERTY()
	FCurrentBuildConfig CurrentBuildConfig;

	// 当前选中的槽位
	UPROPERTY()
	EBuildSlotType CurrentSelectedSlot;

	// 当前选中的物品
	UPROPERTY()
	FBuildItemInfo CurrentSelectedItem;

	// ========== 背包系统引用 ==========

	/** 背包配置数据库引用 */
	UPROPERTY()
	TObjectPtr<UInventoryConfigDB> ConfigDB;

	/** 玩家背包组件引用 */
	UPROPERTY()
	TObjectPtr<UXwInventoryComponent> PlayerInventory;

	// ========== 辅助函数 ==========

	/** 获取背包配置数据库 */
	UInventoryConfigDB* GetInventoryConfigDB() const;

	/** 获取玩家背包组件实例 */
	UXwInventoryComponent* GetPlayerInventoryComponent() const;

	/** 将背包系统的EInventoryType转换为Build系统的EBuildSlotType */
	EBuildSlotType ConvertInventoryTypeToBuildSlotType(EInventoryType InventoryType) const;

	/** 将Build系统的EBuildSlotType转换为背包系统的EInventoryType */
	EInventoryType ConvertBuildSlotTypeToInventoryType(EBuildSlotType SlotType) const;

	// 计算Build的总属性加成
	TMap<FGameplayTag, float> CalculateTotalAttributeModifiers() const;

	// 广播Build配置更新
	void BroadcastBuildConfigUpdate();
};
