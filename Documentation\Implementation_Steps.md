# 角色Build界面实现步骤指南

## 步骤1: 编译和配置C++代码

### 1.1 编译项目

### 1.2 在UE编辑器中配置HUD
1. 打开`Content/UI/HUD/BP_XwHUD`蓝图
2. 在Details面板中找到新增的属性：
   - `Character Build Widget Controller Class`
   - 设置为稍后创建的`BP_CharacterBuildWidgetController`

## 步骤2: 创建蓝图控制器

### 2.1 创建控制器蓝图
1. 在`Content/UI/Controller/`文件夹中
2. 右键 → Blueprint Class → 搜索`CharacterBuildWidgetController`
3. 创建`BP_CharacterBuildWidgetController`
4. 编译并保存

### 2.2 配置HUD引用
1. 打开`Content/UI/HUD/BP_XwHUD`
2. 设置`Character Build Widget Controller Class`为`BP_CharacterBuildWidgetController`

## 步骤3: 创建主界面Widget

### 3.1 创建主Widget
1. 在`Content/UI/`文件夹中
2. 右键 → User Interface → Widget Blueprint
3. 命名为`WBP_CharacterBuild`
4. 在Graph面板中设置Parent Class为`XwUserWidget`

### 3.2 设计根布局
在Designer面板中：
```
Canvas Panel (Root)
├── Image_Background (全屏背景)
│   ├── Anchor: Fill Screen
│   ├── Color: 黑色，Alpha: 0.7
│   └── Material: 可选择暗角效果材质
├── VBox_MainContainer
│   ├── Anchor: Fill Screen
│   ├── Padding: 50px all sides
│   ├── HBox_TopStatusBar (高度: 80px)
│   ├── HBox_MainContent (Fill remaining space)
│   └── HBox_BottomDetail (高度: 120px)
```

## 步骤4: 实现顶部状态栏

### 4.1 状态栏布局
在`HBox_TopStatusBar`中添加：
```
HBox_TopStatusBar
├── HBox_PlayerLevel (Size: Auto)
│   ├── Text_LevelLabel ("Lv.")
│   ├── Text_PlayerLevel ("1")
│   └── CircularProgressBar_XP
├── Spacer (Size: Fill, Ratio: 1.0)
├── VBox_HealthBar (Size: Auto)
│   ├── ProgressBar_Health
│   └── Text_Health ("100/100")
├── VBox_ManaBar (Size: Auto)
│   ├── ProgressBar_Mana  
│   └── Text_Mana ("50/50")
└── HBox_Energy (Size: Auto)
    ├── Image_Energy1 (菱形图标)
    ├── Image_Energy2
    ├── Image_Energy3
    └── Image_Energy4
```

### 4.2 绑定状态栏数据
在Event Graph中：
```cpp
// Event: Widget Controller Set
Event WidgetControllerSet
├── Cast to CharacterBuildWidgetController
├── Bind Event to OnPlayerLevelChanged
├── Bind Event to OnHealthChanged (从基类继承)
├── Bind Event to OnManaChanged (从基类继承)
└── Bind Event to OnEnergyChanged
```

## 步骤5: 创建Build槽位组件

### 5.1 创建WBP_BuildSlot
1. 创建新的Widget Blueprint: `WBP_BuildSlot`
2. Parent Class: `XwUserWidget`

### 5.2 设计槽位布局
```
Button_SlotContainer (Root)
├── Size: 80x80 (武器槽位可以设为100x100)
├── Style: 透明背景
└── Overlay_SlotContent
    ├── Image_Background (圆形背景)
    ├── Image_ItemIcon (物品图标)
    ├── Border_FocusBorder (选中边框)
    └── Text_KeyHint (快捷键提示)
```

### 5.3 槽位蓝图变量
在`WBP_BuildSlot`中添加变量：
```cpp
// Editable Variables
UPROPERTY(EditAnywhere, BlueprintReadWrite)
EBuildSlotType SlotType = EBuildSlotType::None;

UPROPERTY(EditAnywhere, BlueprintReadWrite)
FText KeyHint = FText::GetEmpty();

// Runtime Variables
UPROPERTY(BlueprintReadWrite)
bool bIsSelected = false;

UPROPERTY(BlueprintReadWrite)
FBuildItemInfo CurrentItem;
```

### 5.4 槽位交互逻辑
```cpp
// Event: On Clicked (Button_SlotContainer)
Event OnClicked
├── Get Widget Controller (Cast to CharacterBuildWidgetController)
├── Call SelectSlot (SlotType)
└── Play Animation (SlotClickAnimation)

// Custom Event: Update Slot Display
Event UpdateSlotDisplay
├── Set Image_ItemIcon Texture (CurrentItem.Icon)
├── Set Border_FocusBorder Visibility (bIsSelected ? Visible : Hidden)
└── Update Background Color based on slot state
```

## 步骤6: 实现主内容区域

### 6.1 主内容区域布局
在`HBox_MainContent`中：
```
HBox_MainContent
├── VBox_ItemList (Width: 300px, Visibility: Hidden initially)
│   ├── Text_ListTitle ("Available Items")
│   └── ScrollBox_ItemList
│       └── VBox_ItemContainer (动态填充)
├── Spacer (Width: 50px)
├── VBox_CenterArea (Size: Fill)
│   ├── Image_CharacterAvatar (Height: 200px)
│   └── Canvas_BuildSlots (Height: 400px)
│       ├── WBP_BuildSlot_Weapon (Position: Center)
│       ├── WBP_BuildSlot_Skill1 (Position: Top-Left)
│       ├── WBP_BuildSlot_Skill2 (Position: Top-Right)
│       ├── WBP_BuildSlot_Armor (Position: Left)
│       ├── WBP_BuildSlot_Accessory (Position: Right)
│       ├── WBP_BuildSlot_Item (Position: Bottom-Left)
│       └── WBP_BuildSlot_Buff (Position: Bottom-Right)
├── Spacer (Width: 50px)
└── VBox_Attributes (Width: 250px)
    ├── Text_AttributesTitle ("Attributes")
    └── VBox_AttributeList (动态填充)
```

### 6.2 配置Build槽位
为每个`WBP_BuildSlot`设置：
1. `SlotType`对应的枚举值
2. `KeyHint`对应的快捷键文本
3. 在Canvas中的Position

## 步骤7: 创建物品列表条目

### 7.1 创建WBP_BuildItemEntry
```
Button_ItemContainer (Root)
├── Size: 280x60
├── Style: 列表项样式
└── HBox_ItemContent
    ├── Image_ItemIcon (Size: 50x50)
    ├── VBox_ItemInfo (Size: Fill)
    │   ├── Text_ItemName
    │   └── Text_ItemDescription
    └── Image_EquippedIndicator (Size: 20x20, Visibility: Hidden)
```

### 7.2 物品条目变量和逻辑
```cpp
// Variables
UPROPERTY(BlueprintReadWrite)
FBuildItemInfo ItemInfo;

UPROPERTY(BlueprintReadWrite)
bool bIsEquipped = false;

// Events
Event OnClicked
├── Get Widget Controller
├── Call SelectItem (ItemInfo)
└── Play Selection Animation

Event UpdateItemDisplay
├── Set Text_ItemName (ItemInfo.ItemName)
├── Set Text_ItemDescription (ItemInfo.Description)
├── Set Image_ItemIcon (ItemInfo.Icon)
└── Set Image_EquippedIndicator Visibility (bIsEquipped)
```

## 步骤8: 实现数据绑定

### 8.1 主Widget的控制器绑定
在`WBP_CharacterBuild`的Event Graph中：
```cpp
Event WidgetControllerSet
├── Cast to CharacterBuildWidgetController
├── Store reference as BuildController
├── Bind OnSlotSelected → HandleSlotSelected
├── Bind OnItemSelected → HandleItemSelected  
├── Bind OnAvailableItemsUpdated → HandleAvailableItemsUpdated
├── Bind OnBuildConfigUpdated → HandleBuildConfigUpdated
└── Call BroadcastInitialValue

Function HandleSlotSelected(SlotType)
├── Update all slot selection states
├── Show ItemList (Set Visibility to Visible)
├── Play list show animation
└── Set focus to item list

Function HandleAvailableItemsUpdated(AvailableItems)
├── Clear VBox_ItemContainer children
├── For each item in AvailableItems:
│   ├── Create WBP_BuildItemEntry
│   ├── Set ItemInfo
│   ├── Add to VBox_ItemContainer
│   └── Bind OnClicked event
└── Scroll to current equipped item

Function HandleBuildConfigUpdated(BuildConfig)
├── Update all slot displays
├── Update attribute panel
└── Update character model/avatar
```

## 步骤9: 实现属性面板

### 9.1 创建WBP_AttributeEntry
```
HBox_AttributeEntry (Root)
├── Text_AttributeName (Size: Auto)
├── Spacer (Size: Fill)
└── Text_AttributeValue (Size: Auto)
```

### 9.2 动态创建属性列表
```cpp
Function UpdateAttributePanel()
├── Clear VBox_AttributeList children
├── Get visible attributes from controller
├── For each attribute:
│   ├── Create WBP_AttributeEntry
│   ├── Set attribute name and value
│   └── Add to VBox_AttributeList
```

## 步骤10: 添加动画和效果

### 10.1 创建动画
在Animation面板中创建：
1. `SlotSelectionAnim`: 槽位选中动画
2. `ListShowAnim`: 列表显示动画
3. `ItemSelectionAnim`: 物品选择动画
4. `AttributeUpdateAnim`: 属性更新动画

### 10.2 触发动画
在相应的事件中调用`Play Animation`节点

## 步骤11: 测试和调试

### 11.1 创建测试场景
1. 在GameMode中设置HUD Class为`BP_XwHUD`
2. 创建测试关卡
3. 添加打开Build界面的输入绑定

### 11.2 测试功能
1. 界面显示正确性
2. 槽位选择交互
3. 物品列表显示
4. 属性更新
5. Focus管理
6. 动画效果

## 步骤12: 优化和完善

### 12.1 性能优化
1. 实现对象池
2. 添加Invalidation Box
3. 优化动画性能

### 12.2 用户体验优化
1. 添加音效
2. 完善视觉反馈
3. 添加键盘导航支持

## 注意事项

1. **编译顺序**: 先编译C++代码，再创建蓝图
2. **引用设置**: 确保HUD正确引用控制器蓝图
3. **数据流**: 理解MVC数据流向
4. **性能**: 注意UI更新频率，避免每帧更新
5. **线程安全**: UI更新必须在主线程进行

## 故障排除

1. **编译错误**: 检查头文件包含和前向声明
2. **蓝图错误**: 确保Parent Class设置正确
3. **数据绑定失败**: 检查委托绑定和控制器引用
4. **UI不显示**: 检查Visibility设置和Anchor配置
