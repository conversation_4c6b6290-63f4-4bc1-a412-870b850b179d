#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Character/CharacterDefines.h"
#include "Navigation/Core/XwNavTypes.h"
#include "EnemyNavigationComponent.generated.h"

class UAStarPathFindingBase;
class UJumpPointPathFinding;
struct FJumpPointMap;
class AXwPaperTileMapActor;
class AGridTileMapManager;

/**
 * 敌人导航组件
 * 负责管理敌人的导航功能
 */
UCLASS(ClassGroup=(Navigation), meta=(BlueprintSpawnableComponent))
class DREAMBOY_API UEnemyNavigationComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	UEnemyNavigationComponent();

protected:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:	
	/**
	 * 初始化导航组件
	 * @param InEnemyRace 敌人类型
	 * @return 是否初始化成功
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation")
	bool InitializeNavigation(EEnemyRace InEnemyRace);

	/**
	 * 寻找从起点到终点的路径
	 * @param StartWorldPos 起点世界坐标
	 * @param EndWorldPos 终点世界坐标
	 * @return 路径点数组
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation")
	TArray<FVector> FindPath(const FVector& StartWorldPos, const FVector& EndWorldPos);

	/**
	 * 获取当前所在的TileMap
	 * @return 当前所在的TileMap
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation")
	AXwPaperTileMapActor* GetCurrentTileMap() const;

	/**
	 * 获取寻路对象
	 * @return 寻路对象
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation")
	UAStarPathFindingBase* GetPathFinder() const { return PathFinder; }

	/**
	 * 绘制调试信息
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Debug")
	void DrawDebug();

	/**
	 * 设置是否显示调试信息
	 * @param bShow 是否显示调试信息
	 */
	UFUNCTION(BlueprintCallable, Category = "Navigation|Debug")
	void SetShowDebugInfo(bool bShow);

private:
	/**
	 * 创建寻路对象
	 * @return 是否创建成功
	 */
	bool CreatePathFinder();

	/**
	 * 获取TileMap管理器
	 * @return TileMap管理器
	 */
	AGridTileMapManager* GetTileMapManager() const;

private:
	/** 敌人类型 */
	UPROPERTY(EditAnywhere, Category = "Navigation")
	EEnemyRace EnemyRace;

	/** 寻路对象 */
	UPROPERTY()
	TObjectPtr<UAStarPathFindingBase> PathFinder;

	/** 是否已初始化 */
	bool bInitialized;
}; 