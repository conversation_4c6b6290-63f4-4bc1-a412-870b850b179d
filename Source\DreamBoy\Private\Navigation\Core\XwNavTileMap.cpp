#include "Navigation/Core/XwNavTileMap.h"

FGridTileMap& FGridTileMap::operator=(const FGridTileMap& InMap)
{
	GridWorldOrigin = InMap.GridWorldOrigin;
	GridTileSize = InMap.GridTileSize;
	MapSize = InMap.MapSize;
	TileMap = InMap.TileMap;
	CachedGround = InMap.CachedGround;
	return *this;
}

void FGridTileMap::Init(const TArray<TArray<ENavigationTileType>>& InTileMap, int32 TileSize, FVector WorldOrigin)
{
	if (InTileMap.IsEmpty()) return;

	TileMap = InTileMap;
	MapSize = FIntVector2(InTileMap.Num(), InTileMap[0].Num());
	GridTileSize = TileSize;
	GridWorldOrigin = WorldOrigin;
	InitTiles();
	RefreshCache();
}

void FGridTileMap::InitTiles()
{
	// 2. 具有Block物理的格子正上方的空格子视为地面
	for (int32 Y = 0; Y < MapSize.Y; ++Y)
	{
		for (int32 X = 0; X < MapSize.X; ++X)
		{
			if (TileMap[X][Y] == ENavigationTileType::Empty && CanStandOn(X, Y))
			{
				TileMap[X][Y] = ENavigationTileType::Ground;
			}
		}
	}
}

void FGridTileMap::RefreshCache()
{
	CachedGround.Empty();
	for (int32 Y = 0; Y < MapSize.Y; ++Y)
	{
		for (int32 X = 0; X < MapSize.X; ++X)
		{
			if (TileMap[X][Y] == ENavigationTileType::Ground) {
				CachedGround.Add(FIntVector2(X, Y));
			}
		}
	}
}

bool FGridTileMap::IsValidPos(int Horizon, int Vertical) const
{
	if (!TileMap.IsValidIndex(Horizon) ||
		!TileMap[Horizon].IsValidIndex(Vertical))
		return false;
	else
		return true;
}

ENavigationTileType FGridTileMap::GetTile(int Horizon, int Vertical) const
{
	if (!IsValidPos(Horizon, Vertical)) {
		return ENavigationTileType::Empty;
	}
	else {
		return TileMap[Horizon][Vertical];
	}
}

bool FGridTileMap::CanStandOn(int Horizon, int Vertical) const
{
	ENavigationTileType CurTile = GetTile(Horizon, Vertical);
	if (CurTile == ENavigationTileType::Ground) return true;

	ENavigationTileType BelowTile = GetTile(Horizon, Vertical - 1);
	return CurTile == ENavigationTileType::Empty &&
		(BelowTile == ENavigationTileType::Obstacle || BelowTile == ENavigationTileType::CrossablePlatform);
}

bool FGridTileMap::IsObstacle(int Horizon, int Vertical) const
{
	ENavigationTileType Tile = GetTile(Horizon, Vertical);
	return Tile == ENavigationTileType::Obstacle;
}

bool FGridTileMap::IsGround(int Horizon, int Vertical) const
{
	ENavigationTileType Tile = GetTile(Horizon, Vertical);
	//return Tile != ENavigationTileType::Empty && Tile != ENavigationTileType::Obstacle;
	return Tile == ENavigationTileType::Ground;
}

bool FGridTileMap::HitTest(FIntVector2 UpLeft, FIntVector2 DownRight, TArray<FIntVector2>* HitTiles)
{
	bool IsHit = false;
	for (int X = UpLeft.X; X <= DownRight.X; ++X)
	{
		for (int Z = DownRight.Y; Z <= UpLeft.Y; ++Z)
		{
			if (IsObstacle(X, Z))
			{
				IsHit = true;
				if (HitTiles) {
					HitTiles->Add(FIntVector2(X, Z));
				}
				else {
					// No need to output HitTiles, return immediately
					return IsHit;
				}
			}
		}
	}
	return IsHit;
}

FIntVector2 FGridTileMap::WorldToLocal(const FVector& WorldPos) const
{
	// 原本Original是在Grid的中心位置，为了方便计算，偏移一下到左下角
	FVector RelativePos = WorldPos - (GridWorldOrigin - FVector(GridTileSize / 2.0, 0, GridTileSize / 2.0));
	int32 X = FMath::FloorToInt(RelativePos.X / GridTileSize);
	int32 Z = FMath::FloorToInt(RelativePos.Z / GridTileSize);
	return IsValidPos(X, Z) ? FIntVector2(X, Z) : FIntVector2();
}

FVector FGridTileMap::LocalToWorld(const FIntVector2& LocalPos) const
{
	return GridWorldOrigin +
		FVector(
			LocalPos.X * GridTileSize,
			0,
			LocalPos.Y * GridTileSize
		);
}

bool FGridTileMap::FindNearestGround(const FIntVector2& LocalPos, FIntVector2& OutResult) const
{
	const int PosX = LocalPos.X;
	const int PosZ = LocalPos.Y;
	if (IsGround(LocalPos.X, LocalPos.Y)) {
		OutResult = LocalPos;
		return true;
	}
	int MaxSearchRadius = 4;
	for (int Raidus = 1; Raidus <= MaxSearchRadius; ++Raidus) {
		// Clockwise search start from left
		for (int Z = PosZ; Z < PosZ + Raidus; ++Z) {
			if (IsGround(PosX - Raidus, Z)) {
				OutResult = FIntVector2(PosX - Raidus, Z);
				return true;
			}
		}
		for (int X = PosX - Raidus; X < PosX + Raidus; ++X) {
			if (IsGround(X, PosZ + Raidus)) {
				OutResult = FIntVector2(X, PosZ + Raidus);
				return true;
			}
		}
		for (int Z = PosZ + Raidus; Z > PosZ - Raidus; --Z) {
			if (IsGround(PosX + Raidus, Z)) {
				OutResult = FIntVector2(PosX + Raidus, Z);
				return true;
			}
		}
		for (int X = PosX + Raidus; X > PosX - Raidus; --X) {
			if (IsGround(X, PosZ - Raidus)) {
				OutResult = FIntVector2(X, PosZ - Raidus);
				return true;
			}
		}
		for (int Z = PosZ - Raidus; Z < PosZ; ++Z) {
			if (IsGround(PosX - Raidus, Z)) {
				OutResult = FIntVector2(PosX - Raidus, Z);
				return true;
			}
		}
	}
	return false;
}
