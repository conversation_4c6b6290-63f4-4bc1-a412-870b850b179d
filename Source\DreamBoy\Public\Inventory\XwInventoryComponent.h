// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Inventory/InventoryTypes.h"
#include "XwInventoryComponent.generated.h"

// 前向声明
class UInventoryConfigDB;

// 委托声明
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEntityUnlocked, const FInventoryEntity&, UnlockedEntity);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEntityStatusChanged, const FInventoryEntity&, Entity, bool, bIsNew);

/**
 * 背包收集记录管理器组件
 * 负责记录玩家拥有的所有实体，提供查询和统计功能
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class DREAMBOY_API UXwInventoryComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UXwInventoryComponent();

protected:
    virtual void BeginPlay() override;

public:
    // ========== 解锁实体接口 ==========
    
    /**
     * 解锁实体（通过ConfigID）
     * @param ConfigID 配置ID
     * @return 是否成功解锁
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    bool UnlockEntity(int32 ConfigID);

    /**
     * 解锁实体（通过Type和TypeID）
     * @param Type 实体类型
     * @param TypeID 类型内ID
     * @return 是否成功解锁
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    bool UnlockEntityByType(EInventoryType Type, int32 TypeID);

    // ========== 查询接口 ==========
    
    /**
     * 检查是否拥有指定实体（通过ConfigID）
     * @param ConfigID 配置ID
     * @return 是否拥有
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    bool HasEntity(int32 ConfigID) const;

    /**
     * 检查是否拥有指定实体（通过Type和TypeID）
     * @param Type 实体类型
     * @param TypeID 类型内ID
     * @return 是否拥有
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    bool HasEntityByType(EInventoryType Type, int32 TypeID) const;

    /**
     * 获取实体（通过ConfigID）
     * @param ConfigID 配置ID
     * @return 实体指针，如果不存在则返回nullptr
     */
    const FInventoryEntity* GetEntityByConfigID(int32 ConfigID) const;
    UFUNCTION(BlueprintCallable, meta=(DisplayName = "Get Entity By ConfigID", ScriptName = "GetEntityByConfigID"), Category = "Inventory")
	FInventoryEntity K2_GetEntityByConfigID(int32 ConfigID) const;

    /**
     * 获取实体（通过Type和TypeID）
     * @param Type 实体类型
     * @param TypeID 类型内ID
     * @return 实体指针，如果不存在则返回nullptr
     */
	const FInventoryEntity* GetEntityByType(EInventoryType Type, int32 TypeID) const;
    UFUNCTION(BlueprintCallable, meta=(DisplayName = "Get Entity By Type", ScriptName = "GetEntityByType"), Category = "Inventory")
	FInventoryEntity K2_GetEntityByType(EInventoryType Type, int32 TypeID) const;

    // ========== 分类查询接口 ==========
    
    /**
     * 获取指定类型的所有实体
     * @param Type 实体类型
     * @return 实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetEntitiesByType(EInventoryType Type) const;

    /**
     * 获取所有武器
     * @return 武器实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetWeapons() const { return GetEntitiesByType(EInventoryType::Weapon); }

    /**
     * 获取所有防具
     * @return 防具实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetArmors() const { return GetEntitiesByType(EInventoryType::Armor); }

    /**
     * 获取所有饰品
     * @return 饰品实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetAccessories() const { return GetEntitiesByType(EInventoryType::Accessory); }

    /**
     * 获取所有道具
     * @return 道具实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetItems() const { return GetEntitiesByType(EInventoryType::Item); }

    /**
     * 获取所有技能
     * @return 技能实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetSkills() const { return GetEntitiesByType(EInventoryType::Skill); }

    /**
     * 获取所有Buff
     * @return Buff实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetBuffs() const { return GetEntitiesByType(EInventoryType::Buff); }

    /**
     * 获取所有重要道具
     * @return 重要道具实体数组
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    TArray<FInventoryEntity> GetImportantItems() const { return GetEntitiesByType(EInventoryType::ImportantItem); }

    // ========== 统计接口 ==========
    
    /**
     * 获取已解锁的实体数量
     * @param Type 实体类型
     * @return 已解锁数量
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    int32 GetUnlockedCount(EInventoryType Type) const;

    /**
     * 获取指定类型的总配置数量
     * @param Type 实体类型
     * @return 总配置数量
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    int32 GetTotalCount(EInventoryType Type) const;

    /**
     * 检查指定类型的收集是否完成
     * @param Type 实体类型
     * @return 是否收集完成
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    bool IsCollectionComplete(EInventoryType Type) const;

    /**
     * 获取收集进度（0.0 - 1.0）
     * @param Type 实体类型
     * @return 收集进度
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    float GetCollectionProgress(EInventoryType Type) const;

    // ========== 状态管理接口 ==========
    
    /**
     * 标记实体为已查看（清除新获得标记）
     * @param ConfigID 配置ID
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void MarkEntityAsViewed(int32 ConfigID);

    /**
     * 标记指定类型的所有实体为已查看
     * @param Type 实体类型
     */
    UFUNCTION(BlueprintCallable, Category = "Inventory")
    void MarkAllEntitiesAsViewed(EInventoryType Type);

    // ========== 事件委托 ==========
    
    /** 实体解锁事件 */
    UPROPERTY(BlueprintAssignable, Category = "Inventory|Events")
    FOnEntityUnlocked OnEntityUnlocked;

    /** 实体状态变化事件 */
    UPROPERTY(BlueprintAssignable, Category = "Inventory|Events")
    FOnEntityStatusChanged OnEntityStatusChanged;

protected:
    /**
     * 初始化配置数据库引用
     */
    void InitializeConfigDB();

    /**
     * 根据类型获取对应的存储数组
     * @param Type 实体类型
     * @return 存储数组指针
     */
    TArray<FInventoryEntity>* GetStorageArrayByType(EInventoryType Type);
    const TArray<FInventoryEntity>* GetStorageArrayByType(EInventoryType Type) const;

private:
    // ========== 存储数组 ==========
    
    /** 武器存储 */
    UPROPERTY(SaveGame)
    TArray<FInventoryEntity> Weapons;

    /** 防具存储 */
    UPROPERTY(SaveGame)
    TArray<FInventoryEntity> Armors;

    /** 饰品存储 */
    UPROPERTY(SaveGame)
    TArray<FInventoryEntity> Accessories;

    /** 道具存储 */
    UPROPERTY(SaveGame)
    TArray<FInventoryEntity> Items;

    /** 技能存储 */
    UPROPERTY(SaveGame)
    TArray<FInventoryEntity> Skills;

    /** Buff存储 */
    UPROPERTY(SaveGame)
    TArray<FInventoryEntity> Buffs;

    /** 重要道具存储 */
    UPROPERTY(SaveGame)
    TArray<FInventoryEntity> ImportantItems;

    // ========== 引用 ==========
    
    /** 配置数据库引用 */
    UPROPERTY()
    TObjectPtr<UInventoryConfigDB> ConfigDB;
};
