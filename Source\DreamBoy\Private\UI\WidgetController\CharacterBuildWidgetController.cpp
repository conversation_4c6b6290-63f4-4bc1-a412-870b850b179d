// Fill out your copyright notice in the Description page of Project Settings.

#include "UI/WidgetController/CharacterBuildWidgetController.h"
#include "XwGameplayTags.h"
#include "GAS/GameAttribute/AttributeSetBase.h"
#include "GAS/XwAbilitySystemComponent.h"
#include "Player/XwPlayerState.h"
#include "GameFramework/PlayerController.h"
#include "Inventory/InventoryConfigDB.h"
#include "Inventory/XwInventoryComponent.h"
#include "Character/XwPlayer.h"

void UCharacterBuildWidgetController::BroadcastInitialValue()
{
	// 初始化背包系统引用
	ConfigDB = GetInventoryConfigDB();
	PlayerInventory = GetPlayerInventoryComponent();

	// 广播初始Build配置
	OnBuildConfigUpdated.Broadcast(CurrentBuildConfig);

	// 广播玩家基础属性
	if (UAttributeSetBase* AS = GetXwAS())
	{
		// 这里可以广播玩家等级、经验等信息
		// OnPlayerLevelChanged.Broadcast(GetXwPS()->GetPlayerLevel());
	}
}

void UCharacterBuildWidgetController::BindCallbacksToDependencies()
{
	// 绑定属性变化回调
	if (UAttributeSetBase* AS = GetXwAS())
	{
		// 绑定生命值变化等基础属性
		// 这里可以根据需要绑定特定属性的变化
	}

	// 绑定玩家状态变化
	if (AXwPlayerState* PS = GetXwPS())
	{
		// 这里可以绑定玩家等级、经验变化等
	}
}

void UCharacterBuildWidgetController::SelectSlot(EBuildSlotType SlotType)
{
	// if (CurrentSelectedSlot == SlotType)
	//     return;

	CurrentSelectedSlot = SlotType;
	OnSlotSelected.Broadcast(SlotType);

	// 获取并广播该槽位的可用物品
	TArray<FBuildItemInfo> AvailableItems = GetAvailableItemsForSlot(SlotType);
	OnAvailableItemsUpdated.Broadcast(AvailableItems);

	//// 自动选中当前装备的物品
	// if (CurrentBuildConfig.EquippedItems.Contains(SlotType))
	//{
	//     FBuildItemInfo CurrentEquipped = CurrentBuildConfig.EquippedItems[SlotType];
	//     if (CurrentEquipped.ItemID != -1) // 如果有装备物品
	//     {
	//         SelectItem(CurrentEquipped);
	//     }
	// }
}

void UCharacterBuildWidgetController::FocusItem(const FBuildItemInfo& ItemInfo)
{
	OnItemFocused.Broadcast(ItemInfo);
}

void UCharacterBuildWidgetController::SelectItem(const FBuildItemInfo& ItemInfo)
{
	CurrentSelectedItem = ItemInfo;
	OnItemSelected.Broadcast(ItemInfo);
}

void UCharacterBuildWidgetController::EquipItem(EBuildSlotType SlotType, const FBuildItemInfo& ItemInfo)
{
	// 更新Build配置
	CurrentBuildConfig.EquippedItems[SlotType] = ItemInfo;

	// 广播装备事件
	OnItemEquipped.Broadcast(SlotType, ItemInfo);

	// 广播Build配置更新
	BroadcastBuildConfigUpdate();
}

TArray<FBuildItemInfo> UCharacterBuildWidgetController::GetAvailableItemsForSlot(EBuildSlotType SlotType)
{
	TArray<FBuildItemInfo> AvailableItems;

	// 确保背包系统引用有效
	if (!PlayerInventory || !ConfigDB)
	{
		UE_LOG(LogTemp,
		       Warning,
		       TEXT("UCharacterBuildWidgetController::GetAvailableItemsForSlot - PlayerInventory or ConfigDB is null"));
		return AvailableItems;
	}

	// 将Build槽位类型转换为背包系统的物品类型
	EInventoryType InventoryType = ConvertBuildSlotTypeToInventoryType(SlotType);
	if (InventoryType == EInventoryType::ImportantItem)
	{
		// ImportantItem不参与Build系统
		return AvailableItems;
	}

	// 获取玩家拥有的对应类型物品
	TArray<FInventoryEntity> OwnedEntities;
	switch (InventoryType)
	{
	case EInventoryType::Weapon:
		OwnedEntities = PlayerInventory->GetWeapons();
		break;
	case EInventoryType::Armor:
		OwnedEntities = PlayerInventory->GetArmors();
		break;
	case EInventoryType::Accessory:
		OwnedEntities = PlayerInventory->GetAccessories();
		break;
	case EInventoryType::Item:
		OwnedEntities = PlayerInventory->GetItems();
		break;
	case EInventoryType::Skill:
		OwnedEntities = PlayerInventory->GetSkills();
		break;
	case EInventoryType::Buff:
		OwnedEntities = PlayerInventory->GetBuffs();
		break;
	default:
		break;
	}

	// 转换为FBuildItemInfo格式
	for (const FInventoryEntity& Entity : OwnedEntities)
	{
		FBuildItemInfo BuildItem(SlotType, Entity.ConfigID);
		AvailableItems.Add(BuildItem);
	}

	// 对于技能槽位，需要特殊处理
	if (SlotType == EBuildSlotType::Skill1 || SlotType == EBuildSlotType::Skill2)
	{
		// 两个技能槽位共享同一个技能池
		// 这里可以根据需要添加额外的过滤逻辑
	}

	return AvailableItems;
}

// ========== 背包系统集成接口实现 ==========

FText UCharacterBuildWidgetController::GetItemDisplayName(int32 ItemID) const
{
	if (!ConfigDB)
	{
		return FText::FromString(TEXT("Unknown"));
	}

	const FInventoryEntityConfig* Config = ConfigDB->GetEntityConfig(ItemID);
	if (Config)
	{
		return Config->DisplayName;
	}

	return FText::FromString(TEXT("Unknown"));
}

FText UCharacterBuildWidgetController::GetItemDescription(int32 ItemID) const
{
	if (!ConfigDB)
	{
		return FText::FromString(TEXT("No description available"));
	}

	const FInventoryEntityConfig* Config = ConfigDB->GetEntityConfig(ItemID);
	if (Config)
	{
		return Config->Description;
	}

	return FText::FromString(TEXT("No description available"));
}

UTexture2D* UCharacterBuildWidgetController::GetItemIcon(int32 ItemID) const
{
	if (!ConfigDB)
	{
		return nullptr;
	}

	const FInventoryEntityConfig* Config = ConfigDB->GetEntityConfig(ItemID);
	if (Config && !Config->Icon.IsNull())
	{
		// 使用StaticLoadObject加载资产
		return Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), nullptr, *Config->Icon.ToString()));
	}

	return nullptr;
}

EEntityRarity UCharacterBuildWidgetController::GetItemRarity(int32 ItemID) const
{
	if (!ConfigDB)
	{
		return EEntityRarity::Common;
	}

	const FInventoryEntityConfig* Config = ConfigDB->GetEntityConfig(ItemID);
	if (Config)
	{
		return Config->Rarity;
	}

	return EEntityRarity::Common;
}

bool UCharacterBuildWidgetController::HasItem(int32 ItemID) const
{
	if (!PlayerInventory)
	{
		return false;
	}

	return PlayerInventory->HasEntity(ItemID);
}

// ========== 辅助函数实现 ==========

UInventoryConfigDB* UCharacterBuildWidgetController::GetInventoryConfigDB() const
{
	return UInventoryConfigDB::GetInventoryConfigDB(this);
}

UXwInventoryComponent* UCharacterBuildWidgetController::GetPlayerInventoryComponent() const
{
	if (AXwPlayer* Player = Cast<AXwPlayer>(PlayerController->GetPawn()))
	{
		return Player->FindComponentByClass<UXwInventoryComponent>();
	}
	return nullptr;
}

EBuildSlotType UCharacterBuildWidgetController::ConvertInventoryTypeToBuildSlotType(EInventoryType InventoryType) const
{
	switch (InventoryType)
	{
	case EInventoryType::Weapon:
		return EBuildSlotType::Weapon;
	case EInventoryType::Armor:
		return EBuildSlotType::Armor;
	case EInventoryType::Accessory:
		return EBuildSlotType::Accessory;
	case EInventoryType::Item:
		return EBuildSlotType::Item;
	case EInventoryType::Skill:
		return EBuildSlotType::Skill1; // 默认返回Skill1，具体使用时需要判断
	case EInventoryType::Buff:
		return EBuildSlotType::Buff;
	default:
		return EBuildSlotType::None;
	}
}

EInventoryType UCharacterBuildWidgetController::ConvertBuildSlotTypeToInventoryType(EBuildSlotType SlotType) const
{
	switch (SlotType)
	{
	case EBuildSlotType::Weapon:
		return EInventoryType::Weapon;
	case EBuildSlotType::Armor:
		return EInventoryType::Armor;
	case EBuildSlotType::Accessory:
		return EInventoryType::Accessory;
	case EBuildSlotType::Item:
		return EInventoryType::Item;
	case EBuildSlotType::Skill1:
	case EBuildSlotType::Skill2:
		return EInventoryType::Skill;
	case EBuildSlotType::Buff:
		return EInventoryType::Buff;
	default:
		return EInventoryType::ImportantItem; // 使用ImportantItem作为无效类型
	}
}

TMap<FGameplayTag, float> UCharacterBuildWidgetController::CalculateTotalAttributeModifiers() const
{
	TMap<FGameplayTag, float> TotalModifiers;

	// 遍历所有装备的物品，累加属性加成
	for (const auto& SlotPair : CurrentBuildConfig.EquippedItems)
	{
		const FBuildItemInfo& Item = SlotPair.Value;
		if (Item.IsValid()) // 使用新的IsValid方法
		{
			// 这里可以根据ItemID从配置中获取属性加成
			// 目前暂时不实现，因为背包系统的配置中没有属性加成信息
			// 如果需要，可以在FInventoryEntityConfig中添加属性加成字段
		}
	}

	return TotalModifiers;
}

void UCharacterBuildWidgetController::BroadcastBuildConfigUpdate()
{
	OnBuildConfigUpdated.Broadcast(CurrentBuildConfig);

	// 这里可以触发属性重新计算和更新
	// 例如：通知GAS系统应用新的属性加成
}
