// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "XwHUD.generated.h"

class UXwUserWidget;
class UOverlayWidgetController;
class UAttributeMenuWidgetController;
class UCharacterBuildWidgetController;
struct FWidgetControllerParams;
class APlayerController;
class APlayerState;
class UAbilitySystemComponent;
class UAttributeSet;

/**
 * 连接UI MVC 的中间件。
 * 定位M的Player把数据包装成FWidgetControllerParams传过来
 * 定位V的UserWidget在此被创建，AddToViewport
 * 定位C的WidgetController在此被创建
 * 最后把这三个对象连接起来，互相赋予引用
 */
UCLASS()
class DREAMBOY_API AXwHUD : public AHUD
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintCallable)
	UOverlayWidgetController *GetOverlayWidgetController(const FWidgetControllerParams &WCParams);
	UFUNCTION(BlueprintCallable)
	UAttributeMenuWidgetController *GetAttributeMenuWidgetController(const FWidgetControllerParams &WCParams);
	UFUNCTION(BlueprintCallable)
	UCharacterBuildWidgetController *GetCharacterBuildWidgetController(const FWidgetControllerParams &WCParams);

	void InitOverlay(APlayerController *PC, APlayerState *PS, UAbilitySystemComponent *ASC, UAttributeSet *AS);

private:
	UPROPERTY()
	TObjectPtr<UXwUserWidget> OverlayWidget;
	UPROPERTY(EditAnywhere)
	TSubclassOf<UXwUserWidget> OverlayWidgetClass;

	UPROPERTY()
	TObjectPtr<UOverlayWidgetController> OverlayWidgetController;
	UPROPERTY(EditAnywhere)
	TSubclassOf<UOverlayWidgetController> OverlayWidgetControllerClass;

	UPROPERTY()
	TObjectPtr<UAttributeMenuWidgetController> AttributeMenuWidgetController;
	UPROPERTY(EditAnywhere)
	TSubclassOf<UAttributeMenuWidgetController> AttributeMenuWidgetControllerClass;

	UPROPERTY()
	TObjectPtr<UCharacterBuildWidgetController> CharacterBuildWidgetController;
	UPROPERTY(EditAnywhere)
	TSubclassOf<UCharacterBuildWidgetController> CharacterBuildWidgetControllerClass;
};
